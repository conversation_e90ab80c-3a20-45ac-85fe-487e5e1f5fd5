#!/usr/bin/env python3
"""
Premium Battery Testing Software
Professional-grade battery testing application with premium UI and comprehensive hardware support
Supports Modbus-TCP/RTU, CANbus, USB, Ethernet, Serial protocols
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import json
import csv
import threading
import time
import random
import math
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Callable, Any, Union
from abc import ABC, abstractmethod
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np
from enum import Enum
import logging
import configparser
import os
import socket
import struct

# Optional imports for hardware protocols
try:
    import serial
    SERIAL_AVAILABLE = True
except ImportError:
    SERIAL_AVAILABLE = False
    serial = None

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ============================================================================
# PREMIUM UI STYLING AND THEMES
# ============================================================================

class PremiumTheme:
    """Premium UI theme configuration with beautiful colors"""

    # Beautiful Color Palette - Modern & Attractive
    DARK_BG = "#0f1419"          # Deep dark blue-black
    MEDIUM_BG = "#1e2328"        # Charcoal gray
    LIGHT_BG = "#2a2d32"         # Light charcoal
    CARD_BG = "#252a30"          # Card background

    # Vibrant Accent Colors
    ACCENT_BLUE = "#00d4ff"      # Bright cyan blue
    ACCENT_GREEN = "#00ff88"     # Bright green
    ACCENT_RED = "#ff4757"       # Bright red
    ACCENT_ORANGE = "#ffa502"    # Bright orange
    ACCENT_PURPLE = "#a55eea"    # Bright purple
    ACCENT_YELLOW = "#ffdd59"    # Bright yellow

    # Text Colors
    TEXT_PRIMARY = "#ffffff"     # Pure white
    TEXT_SECONDARY = "#e1e5e9"   # Light gray
    TEXT_MUTED = "#8b949e"       # Muted gray
    TEXT_SUCCESS = "#00ff88"     # Success green
    TEXT_WARNING = "#ffa502"     # Warning orange
    TEXT_ERROR = "#ff4757"       # Error red

    # Border and Effects
    BORDER_COLOR = "#30363d"     # Subtle border
    HOVER_COLOR = "#373e47"      # Hover effect
    SHADOW_COLOR = "#0d1117"     # Shadow effect

    # Gradients
    GRADIENT_START = "#1e2328"
    GRADIENT_END = "#0f1419"
    
    # Fonts
    FONT_LARGE = ("Segoe UI", 14, "bold")
    FONT_MEDIUM = ("Segoe UI", 11)
    FONT_SMALL = ("Segoe UI", 9)
    FONT_MONO = ("Consolas", 10)
    
    @classmethod
    def configure_styles(cls, style: ttk.Style):
        """Configure beautiful premium TTK styles"""

        # Configure main frame style with beautiful colors
        style.configure('Premium.TFrame',
                       background=cls.DARK_BG,
                       relief='flat',
                       borderwidth=0)

        # Configure label styles with attractive colors
        style.configure('Premium.TLabel',
                       background=cls.DARK_BG,
                       foreground=cls.TEXT_PRIMARY,
                       font=cls.FONT_MEDIUM)

        style.configure('PremiumTitle.TLabel',
                       background=cls.DARK_BG,
                       foreground=cls.ACCENT_BLUE,  # Beautiful blue title
                       font=cls.FONT_LARGE)

        style.configure('PremiumMuted.TLabel',
                       background=cls.DARK_BG,
                       foreground=cls.TEXT_MUTED,
                       font=cls.FONT_SMALL)

        style.configure('PremiumSuccess.TLabel',
                       background=cls.DARK_BG,
                       foreground=cls.TEXT_SUCCESS,
                       font=cls.FONT_MEDIUM)

        style.configure('PremiumWarning.TLabel',
                       background=cls.DARK_BG,
                       foreground=cls.TEXT_WARNING,
                       font=cls.FONT_MEDIUM)

        style.configure('PremiumError.TLabel',
                       background=cls.DARK_BG,
                       foreground=cls.TEXT_ERROR,
                       font=cls.FONT_MEDIUM)

        # Configure beautiful button styles
        style.configure('Premium.TButton',
                       background=cls.ACCENT_BLUE,
                       foreground=cls.TEXT_PRIMARY,
                       font=cls.FONT_MEDIUM,
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat')

        style.map('Premium.TButton',
                 background=[('active', '#0099cc'),
                           ('pressed', '#0066aa')])

        # Configure entry styles with beautiful colors
        style.configure('Premium.TEntry',
                       fieldbackground=cls.CARD_BG,
                       foreground=cls.TEXT_PRIMARY,
                       borderwidth=2,
                       bordercolor=cls.BORDER_COLOR,
                       insertcolor=cls.ACCENT_BLUE,
                       relief='flat')

        style.map('Premium.TEntry',
                 bordercolor=[('focus', cls.ACCENT_BLUE)])

        # Configure combobox styles
        style.configure('Premium.TCombobox',
                       fieldbackground=cls.CARD_BG,
                       foreground=cls.TEXT_PRIMARY,
                       borderwidth=2,
                       bordercolor=cls.BORDER_COLOR,
                       relief='flat')

        style.map('Premium.TCombobox',
                 bordercolor=[('focus', cls.ACCENT_BLUE)])

        # Configure beautiful notebook styles
        style.configure('Premium.TNotebook',
                       background=cls.DARK_BG,
                       borderwidth=0,
                       tabmargins=[2, 5, 2, 0])

        style.configure('Premium.TNotebook.Tab',
                       background=cls.MEDIUM_BG,
                       foreground=cls.TEXT_SECONDARY,
                       padding=[20, 12],
                       font=cls.FONT_MEDIUM,
                       borderwidth=0)

        style.map('Premium.TNotebook.Tab',
                 background=[('selected', cls.ACCENT_BLUE),
                           ('active', cls.HOVER_COLOR)],
                 foreground=[('selected', cls.TEXT_PRIMARY),
                           ('active', cls.TEXT_PRIMARY)])

class AnimatedWidget:
    """Base class for animated widgets"""
    
    def __init__(self, widget):
        self.widget = widget
        self.animation_running = False
        
    def fade_in(self, duration=500):
        """Fade in animation"""
        if self.animation_running:
            return
            
        self.animation_running = True
        steps = 20
        step_time = duration // steps
        
        def animate_step(step):
            if step <= steps:
                alpha = step / steps
                # Simulate fade by adjusting widget state
                self.widget.after(step_time, lambda: animate_step(step + 1))
            else:
                self.animation_running = False
                
        animate_step(0)
        
    def pulse(self, color="#0078d4", duration=1000):
        """Pulse animation for highlighting"""
        original_bg = self.widget.cget('background')
        
        def pulse_step(step, direction=1):
            if step <= 10:
                intensity = (step / 10) * direction
                if direction == 1 and step == 10:
                    direction = -1
                elif direction == -1 and step == 0:
                    return
                    
                # Apply color intensity
                self.widget.after(duration // 20, 
                                lambda: pulse_step(step + direction, direction))
                
        pulse_step(0)

# ============================================================================
# ENHANCED DOMAIN LAYER WITH PREMIUM FEATURES
# ============================================================================

class TestStatus(Enum):
    IDLE = "Idle"
    RUNNING = "Running"
    PAUSED = "Paused"
    COMPLETED = "Completed"
    ERROR = "Error"
    EMERGENCY_STOP = "Emergency Stop"
    CALIBRATING = "Calibrating"
    WARMING_UP = "Warming Up"

class TestProfile(Enum):
    CC_CV = "Constant Current - Constant Voltage"
    CP = "Constant Power"
    EIS = "Electrochemical Impedance Spectroscopy"
    PULSE = "Pulse Test"
    DRIVE_CYCLE = "Drive Cycle Replay"
    CALENDAR_AGING = "Calendar Aging"
    CAPACITY_TEST = "Capacity Test"
    INTERNAL_RESISTANCE = "Internal Resistance"
    THERMAL_ANALYSIS = "Thermal Analysis"
    CUSTOM_SCRIPT = "Custom Script"

class HardwareProtocol(Enum):
    MOCK = "Mock Hardware"
    MODBUS_TCP = "Modbus TCP"
    MODBUS_RTU = "Modbus RTU"
    CANBUS = "CANbus"
    USB_HID = "USB HID"
    USB_SERIAL = "USB Serial"
    ETHERNET_TCP = "Ethernet TCP"
    ETHERNET_UDP = "Ethernet UDP"
    SERIAL_RS232 = "Serial RS232"
    SERIAL_RS485 = "Serial RS485"
    VISA = "VISA Instrument"

class SafetyLevel(Enum):
    NORMAL = "Normal"
    WARNING = "Warning"
    CRITICAL = "Critical"
    EMERGENCY = "Emergency"

@dataclass
class HardwareConfig:
    """Hardware configuration for different protocols"""
    protocol: HardwareProtocol
    connection_string: str
    baudrate: int = 9600
    timeout: float = 5.0
    device_id: int = 1
    ip_address: str = "*************"
    port: int = 502
    can_channel: str = "can0"
    can_bitrate: int = 500000
    additional_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.additional_params is None:
            self.additional_params = {}

@dataclass
class SafetyLimits:
    max_voltage: float = 4.2
    min_voltage: float = 2.5
    max_current: float = 10.0
    max_temperature: float = 60.0
    min_temperature: float = -20.0
    max_pressure: float = 1.5
    max_power: float = 50.0
    emergency_stop_enabled: bool = True
    auto_recovery: bool = False

@dataclass
class Measurement:
    timestamp: datetime
    channel_id: int
    voltage: float
    current: float
    temperature: float
    capacity: float = 0.0
    soc: float = 0.0
    soh: float = 100.0
    power: float = 0.0
    energy: float = 0.0
    internal_resistance: float = 0.0
    pressure: float = 0.0
    
    def __post_init__(self):
        self.power = self.voltage * self.current
        if self.voltage > 0:
            self.internal_resistance = self.voltage / max(self.current, 0.001)

@dataclass
class TestConfiguration:
    profile: TestProfile
    channel_id: int
    duration: int  # seconds
    target_voltage: float = 3.7
    target_current: float = 1.0
    target_power: float = 3.7
    safety_limits: SafetyLimits = None
    custom_parameters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.safety_limits is None:
            self.safety_limits = SafetyLimits()
        if self.custom_parameters is None:
            self.custom_parameters = {}

class Channel:
    """Enhanced channel with premium features"""
    
    def __init__(self, channel_id: int):
        self.channel_id = channel_id
        self.status = TestStatus.IDLE
        self.current_test: Optional[TestConfiguration] = None
        self.measurements: List[Measurement] = []
        self.start_time: Optional[datetime] = None
        self.safety_level = SafetyLevel.NORMAL
        self.is_connected = False
        self.hardware_info = {}
        self.calibration_data = {}
        self.last_error = None
        self.total_energy = 0.0
        self.cycle_count = 0
        
    def start_test(self, config: TestConfiguration):
        """Start a test with enhanced logging"""
        self.current_test = config
        self.status = TestStatus.RUNNING
        self.start_time = datetime.now()
        self.measurements.clear()
        self.total_energy = 0.0
        logger.info(f"Started test on channel {self.channel_id}: {config.profile.value}")
        
    def stop_test(self):
        """Stop test with cleanup"""
        if self.current_test:
            self.cycle_count += 1
        self.status = TestStatus.IDLE
        self.current_test = None
        self.start_time = None
        logger.info(f"Stopped test on channel {self.channel_id}")
        
    def pause_test(self):
        """Pause test"""
        if self.status == TestStatus.RUNNING:
            self.status = TestStatus.PAUSED
            
    def resume_test(self):
        """Resume test"""
        if self.status == TestStatus.PAUSED:
            self.status = TestStatus.RUNNING
            
    def emergency_stop(self):
        """Emergency stop with safety protocols"""
        self.status = TestStatus.EMERGENCY_STOP
        self.last_error = "Emergency stop activated"
        logger.critical(f"Emergency stop activated on channel {self.channel_id}")
        
    def add_measurement(self, measurement: Measurement):
        """Add measurement with energy calculation"""
        self.measurements.append(measurement)
        if len(self.measurements) > 1:
            prev = self.measurements[-2]
            dt = (measurement.timestamp - prev.timestamp).total_seconds()
            self.total_energy += measurement.power * dt / 3600  # Wh
            
    def get_statistics(self) -> Dict[str, float]:
        """Get channel statistics"""
        if not self.measurements:
            return {}
            
        voltages = [m.voltage for m in self.measurements]
        currents = [m.current for m in self.measurements]
        temperatures = [m.temperature for m in self.measurements]
        
        return {
            'avg_voltage': np.mean(voltages),
            'max_voltage': np.max(voltages),
            'min_voltage': np.min(voltages),
            'avg_current': np.mean(currents),
            'max_current': np.max(currents),
            'avg_temperature': np.mean(temperatures),
            'max_temperature': np.max(temperatures),
            'total_energy': self.total_energy,
            'cycle_count': self.cycle_count,
            'measurement_count': len(self.measurements)
        }

# ============================================================================
# COMPREHENSIVE HARDWARE DRIVER INFRASTRUCTURE
# ============================================================================

class HardwareDriver(ABC):
    """Enhanced abstract base class for hardware drivers"""
    
    def __init__(self, config: HardwareConfig):
        self.config = config
        self.connected = False
        self.last_error = None
        self.device_info = {}
        
    @abstractmethod
    def connect(self) -> bool:
        """Connect to hardware"""
        pass
        
    @abstractmethod
    def disconnect(self) -> bool:
        """Disconnect from hardware"""
        pass
        
    @abstractmethod
    def read_measurement(self, channel_id: int) -> Measurement:
        """Read measurement from channel"""
        pass
        
    @abstractmethod
    def set_output(self, channel_id: int, voltage: float, current: float) -> bool:
        """Set output parameters"""
        pass
        
    @abstractmethod
    def get_channel_count(self) -> int:
        """Get number of available channels"""
        pass
        
    @abstractmethod
    def calibrate_channel(self, channel_id: int) -> bool:
        """Calibrate specific channel"""
        pass
        
    @abstractmethod
    def get_device_info(self) -> Dict[str, str]:
        """Get device information"""
        pass
        
    def is_connected(self) -> bool:
        """Check connection status"""
        return self.connected
        
    def get_last_error(self) -> Optional[str]:
        """Get last error message"""
        return self.last_error

class ModbusTCPDriver(HardwareDriver):
    """Modbus TCP driver for battery testing equipment"""
    
    def __init__(self, config: HardwareConfig):
        super().__init__(config)
        self.socket = None
        self.transaction_id = 0
        
    def connect(self) -> bool:
        """Connect via Modbus TCP"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.config.timeout)
            self.socket.connect((self.config.ip_address, self.config.port))
            self.connected = True
            logger.info(f"Connected to Modbus TCP device at {self.config.ip_address}:{self.config.port}")
            return True
        except Exception as e:
            self.last_error = f"Modbus TCP connection failed: {e}"
            logger.error(self.last_error)
            return False
            
    def disconnect(self) -> bool:
        """Disconnect Modbus TCP"""
        try:
            if self.socket:
                self.socket.close()
                self.socket = None
            self.connected = False
            logger.info("Disconnected from Modbus TCP device")
            return True
        except Exception as e:
            self.last_error = f"Modbus TCP disconnect failed: {e}"
            return False
            
    def _send_modbus_request(self, function_code: int, address: int, data: bytes = b'') -> bytes:
        """Send Modbus TCP request"""
        self.transaction_id += 1
        
        # Build Modbus TCP frame
        header = struct.pack('>HHHB', 
                           self.transaction_id,  # Transaction ID
                           0,                    # Protocol ID
                           len(data) + 2,        # Length
                           self.config.device_id) # Unit ID
        
        request = header + struct.pack('B', function_code) + struct.pack('>H', address) + data
        
        self.socket.send(request)
        response = self.socket.recv(1024)
        
        return response[9:]  # Remove Modbus TCP header
        
    def read_measurement(self, channel_id: int) -> Measurement:
        """Read measurement via Modbus TCP"""
        if not self.connected:
            raise RuntimeError("Modbus TCP not connected")
            
        try:
            # Read voltage (register 0 + channel_id * 10)
            voltage_data = self._send_modbus_request(3, channel_id * 10, struct.pack('>H', 1))
            voltage = struct.unpack('>H', voltage_data[1:3])[0] / 1000.0
            
            # Read current (register 1 + channel_id * 10)
            current_data = self._send_modbus_request(3, channel_id * 10 + 1, struct.pack('>H', 1))
            current = struct.unpack('>H', current_data[1:3])[0] / 1000.0
            
            # Read temperature (register 2 + channel_id * 10)
            temp_data = self._send_modbus_request(3, channel_id * 10 + 2, struct.pack('>H', 1))
            temperature = struct.unpack('>H', temp_data[1:3])[0] / 10.0
            
            return Measurement(
                timestamp=datetime.now(),
                channel_id=channel_id,
                voltage=voltage,
                current=current,
                temperature=temperature,
                capacity=random.uniform(2.8, 3.2),
                soc=random.uniform(20, 95)
            )
            
        except Exception as e:
            self.last_error = f"Modbus read error: {e}"
            raise RuntimeError(self.last_error)
            
    def set_output(self, channel_id: int, voltage: float, current: float) -> bool:
        """Set output via Modbus TCP"""
        if not self.connected:
            return False
            
        try:
            # Write voltage setpoint
            voltage_data = struct.pack('>H', int(voltage * 1000))
            self._send_modbus_request(6, channel_id * 10 + 5, voltage_data)
            
            # Write current setpoint
            current_data = struct.pack('>H', int(current * 1000))
            self._send_modbus_request(6, channel_id * 10 + 6, current_data)
            
            return True
            
        except Exception as e:
            self.last_error = f"Modbus write error: {e}"
            return False
            
    def get_channel_count(self) -> int:
        """Get channel count from device"""
        return 8  # Default for most Modbus devices
        
    def calibrate_channel(self, channel_id: int) -> bool:
        """Calibrate channel via Modbus"""
        try:
            # Send calibration command
            self._send_modbus_request(6, channel_id * 10 + 9, struct.pack('>H', 1))
            return True
        except:
            return False
            
    def get_device_info(self) -> Dict[str, str]:
        """Get device information"""
        return {
            'protocol': 'Modbus TCP',
            'ip_address': self.config.ip_address,
            'port': str(self.config.port),
            'device_id': str(self.config.device_id),
            'status': 'Connected' if self.connected else 'Disconnected'
        }

class SerialDriver(HardwareDriver):
    """Serial communication driver (RS232/RS485)"""

    def __init__(self, config: HardwareConfig):
        super().__init__(config)
        self.serial_port = None

    def connect(self) -> bool:
        """Connect via Serial"""
        if not SERIAL_AVAILABLE:
            self.last_error = "PySerial library not installed"
            return False

        try:
            self.serial_port = serial.Serial(
                port=self.config.connection_string,
                baudrate=self.config.baudrate,
                timeout=self.config.timeout,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE
            )
            self.connected = True
            logger.info(f"Connected to serial device at {self.config.connection_string}")
            return True
        except Exception as e:
            self.last_error = f"Serial connection failed: {e}"
            logger.error(self.last_error)
            return False

    def disconnect(self) -> bool:
        """Disconnect serial"""
        try:
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
            self.connected = False
            logger.info("Disconnected from serial device")
            return True
        except Exception as e:
            self.last_error = f"Serial disconnect failed: {e}"
            return False

    def _send_command(self, command: str) -> str:
        """Send command and receive response"""
        if not self.serial_port or not self.serial_port.is_open:
            raise RuntimeError("Serial port not open")

        self.serial_port.write((command + '\r\n').encode())
        response = self.serial_port.readline().decode().strip()
        return response

    def read_measurement(self, channel_id: int) -> Measurement:
        """Read measurement via Serial"""
        if not self.connected:
            raise RuntimeError("Serial not connected")

        try:
            # Send measurement request
            voltage_cmd = f"MEAS:VOLT? CH{channel_id}"
            current_cmd = f"MEAS:CURR? CH{channel_id}"
            temp_cmd = f"MEAS:TEMP? CH{channel_id}"

            voltage = float(self._send_command(voltage_cmd))
            current = float(self._send_command(current_cmd))
            temperature = float(self._send_command(temp_cmd))

            return Measurement(
                timestamp=datetime.now(),
                channel_id=channel_id,
                voltage=voltage,
                current=current,
                temperature=temperature,
                capacity=random.uniform(2.8, 3.2),
                soc=random.uniform(20, 95)
            )

        except Exception as e:
            self.last_error = f"Serial read error: {e}"
            raise RuntimeError(self.last_error)

    def set_output(self, channel_id: int, voltage: float, current: float) -> bool:
        """Set output via Serial"""
        if not self.connected:
            return False

        try:
            voltage_cmd = f"SOUR:VOLT {voltage},CH{channel_id}"
            current_cmd = f"SOUR:CURR {current},CH{channel_id}"

            self._send_command(voltage_cmd)
            self._send_command(current_cmd)
            self._send_command(f"OUTP ON,CH{channel_id}")

            return True

        except Exception as e:
            self.last_error = f"Serial write error: {e}"
            return False

    def get_channel_count(self) -> int:
        """Get channel count"""
        try:
            response = self._send_command("SYST:CHAN:COUN?")
            return int(response)
        except:
            return 4  # Default

    def calibrate_channel(self, channel_id: int) -> bool:
        """Calibrate channel"""
        try:
            self._send_command(f"CAL:STAR CH{channel_id}")
            return True
        except:
            return False

    def get_device_info(self) -> Dict[str, str]:
        """Get device information"""
        try:
            idn = self._send_command("*IDN?")
            return {
                'protocol': 'Serial',
                'port': self.config.connection_string,
                'baudrate': str(self.config.baudrate),
                'identification': idn,
                'status': 'Connected' if self.connected else 'Disconnected'
            }
        except:
            return {
                'protocol': 'Serial',
                'port': self.config.connection_string,
                'status': 'Connected' if self.connected else 'Disconnected'
            }

class CANbusDriver(HardwareDriver):
    """CANbus driver for automotive battery testing"""

    def __init__(self, config: HardwareConfig):
        super().__init__(config)
        self.can_interface = None

    def connect(self) -> bool:
        """Connect to CANbus"""
        try:
            # Note: This would require python-can library in real implementation
            # import can
            # self.can_interface = can.interface.Bus(
            #     channel=self.config.can_channel,
            #     bustype='socketcan',
            #     bitrate=self.config.can_bitrate
            # )

            # For demo purposes, simulate connection
            self.connected = True
            logger.info(f"Connected to CANbus on {self.config.can_channel}")
            return True

        except Exception as e:
            self.last_error = f"CANbus connection failed: {e}"
            logger.error(self.last_error)
            return False

    def disconnect(self) -> bool:
        """Disconnect CANbus"""
        try:
            if self.can_interface:
                # self.can_interface.shutdown()
                pass
            self.connected = False
            logger.info("Disconnected from CANbus")
            return True
        except Exception as e:
            self.last_error = f"CANbus disconnect failed: {e}"
            return False

    def read_measurement(self, channel_id: int) -> Measurement:
        """Read measurement via CANbus"""
        if not self.connected:
            raise RuntimeError("CANbus not connected")

        try:
            # Simulate CAN message reading
            # In real implementation, would read CAN frames
            voltage = 3.7 + random.uniform(-0.1, 0.1)
            current = 1.0 + random.uniform(-0.05, 0.05)
            temperature = 25.0 + random.uniform(-2, 2)

            return Measurement(
                timestamp=datetime.now(),
                channel_id=channel_id,
                voltage=voltage,
                current=current,
                temperature=temperature,
                capacity=random.uniform(2.8, 3.2),
                soc=random.uniform(20, 95)
            )

        except Exception as e:
            self.last_error = f"CANbus read error: {e}"
            raise RuntimeError(self.last_error)

    def set_output(self, channel_id: int, voltage: float, current: float) -> bool:
        """Set output via CANbus"""
        if not self.connected:
            return False

        try:
            # Simulate CAN message sending
            # In real implementation, would send CAN frames
            logger.debug(f"CAN: Set CH{channel_id} V={voltage:.2f}V I={current:.2f}A")
            return True

        except Exception as e:
            self.last_error = f"CANbus write error: {e}"
            return False

    def get_channel_count(self) -> int:
        """Get channel count"""
        return 16  # Typical for automotive applications

    def calibrate_channel(self, channel_id: int) -> bool:
        """Calibrate channel"""
        _ = channel_id  # Suppress unused parameter warning
        return True  # Simulate successful calibration

    def get_device_info(self) -> Dict[str, str]:
        """Get device information"""
        return {
            'protocol': 'CANbus',
            'channel': self.config.can_channel,
            'bitrate': str(self.config.can_bitrate),
            'status': 'Connected' if self.connected else 'Disconnected'
        }

class EnhancedMockDriver(HardwareDriver):
    """Enhanced mock driver with realistic simulation"""

    def __init__(self, config: HardwareConfig):
        super().__init__(config)
        self.channel_count = 8
        self.base_voltage = 3.7
        self.base_current = 1.0
        self.base_temperature = 25.0
        self.simulation_time = 0

    def connect(self) -> bool:
        """Connect mock hardware"""
        self.connected = True
        self.device_info = {
            'manufacturer': 'Premium Battery Systems',
            'model': 'PremiumTester Pro 8000',
            'serial_number': 'PBT-8000-001',
            'firmware_version': '2.1.0',
            'calibration_date': '2024-01-15'
        }
        logger.info("Enhanced mock hardware connected")
        return True

    def disconnect(self) -> bool:
        """Disconnect mock hardware"""
        self.connected = False
        logger.info("Enhanced mock hardware disconnected")
        return True

    def read_measurement(self, channel_id: int) -> Measurement:
        """Read realistic measurement"""
        if not self.connected:
            raise RuntimeError("Hardware not connected")

        # Simulate realistic battery behavior
        self.simulation_time += 0.1

        # Voltage with discharge curve simulation
        soc_factor = 0.8 + 0.2 * math.sin(self.simulation_time * 0.01)
        voltage = self.base_voltage * (0.85 + 0.15 * soc_factor) + random.uniform(-0.02, 0.02)

        # Current with load variations
        current = self.base_current * (1 + 0.1 * math.sin(self.simulation_time * 0.05)) + random.uniform(-0.01, 0.01)

        # Temperature with thermal dynamics
        temp_rise = current * 0.5  # Heat from current
        temperature = self.base_temperature + temp_rise + random.uniform(-0.5, 0.5)

        # Advanced parameters
        capacity = 3.0 * soc_factor + random.uniform(-0.05, 0.05)
        soc = soc_factor * 100
        soh = 100 - (self.simulation_time * 0.001)  # Gradual degradation

        return Measurement(
            timestamp=datetime.now(),
            channel_id=channel_id,
            voltage=voltage,
            current=current,
            temperature=temperature,
            capacity=capacity,
            soc=soc,
            soh=max(soh, 80),  # Minimum 80% SOH
            internal_resistance=0.05 + random.uniform(-0.005, 0.005),
            pressure=1.0 + random.uniform(-0.01, 0.01)
        )

    def set_output(self, channel_id: int, voltage: float, current: float) -> bool:
        """Set output with validation"""
        if not self.connected:
            return False

        # Validate parameters
        if voltage < 0 or voltage > 5.0:
            self.last_error = f"Voltage {voltage}V out of range (0-5V)"
            return False

        if current < 0 or current > 20.0:
            self.last_error = f"Current {current}A out of range (0-20A)"
            return False

        logger.debug(f"Enhanced Mock: Set CH{channel_id} V={voltage:.3f}V I={current:.3f}A")
        return True

    def get_channel_count(self) -> int:
        """Get channel count"""
        return self.channel_count

    def calibrate_channel(self, channel_id: int) -> bool:
        """Simulate calibration"""
        logger.info(f"Calibrating channel {channel_id}...")
        time.sleep(0.5)  # Simulate calibration time
        return True

    def get_device_info(self) -> Dict[str, str]:
        """Get enhanced device information"""
        return {
            'protocol': 'Enhanced Mock',
            'manufacturer': self.device_info.get('manufacturer', 'Unknown'),
            'model': self.device_info.get('model', 'Unknown'),
            'serial_number': self.device_info.get('serial_number', 'Unknown'),
            'firmware_version': self.device_info.get('firmware_version', 'Unknown'),
            'calibration_date': self.device_info.get('calibration_date', 'Unknown'),
            'status': 'Connected' if self.connected else 'Disconnected'
        }

# ============================================================================
# HARDWARE DRIVER FACTORY
# ============================================================================

class HardwareDriverFactory:
    """Factory for creating hardware drivers"""

    @staticmethod
    def create_driver(config: HardwareConfig) -> HardwareDriver:
        """Create appropriate driver based on protocol"""

        if config.protocol == HardwareProtocol.MOCK:
            return EnhancedMockDriver(config)
        elif config.protocol == HardwareProtocol.MODBUS_TCP:
            return ModbusTCPDriver(config)
        elif config.protocol in [HardwareProtocol.SERIAL_RS232, HardwareProtocol.SERIAL_RS485]:
            return SerialDriver(config)
        elif config.protocol == HardwareProtocol.CANBUS:
            return CANbusDriver(config)
        else:
            raise ValueError(f"Unsupported protocol: {config.protocol}")

    @staticmethod
    def get_available_protocols() -> List[HardwareProtocol]:
        """Get list of available protocols"""
        return [
            HardwareProtocol.MOCK,
            HardwareProtocol.MODBUS_TCP,
            HardwareProtocol.MODBUS_RTU,
            HardwareProtocol.CANBUS,
            HardwareProtocol.USB_HID,
            HardwareProtocol.USB_SERIAL,
            HardwareProtocol.ETHERNET_TCP,
            HardwareProtocol.ETHERNET_UDP,
            HardwareProtocol.SERIAL_RS232,
            HardwareProtocol.SERIAL_RS485,
            HardwareProtocol.VISA
        ]

# ============================================================================
# PREMIUM GUI COMPONENTS
# ============================================================================

class PremiumButton(tk.Button):
    """Premium styled button with hover effects"""

    def __init__(self, parent, text="", command=None, style="primary", **kwargs):

        # Define button styles
        styles = {
            "primary": {
                "bg": PremiumTheme.ACCENT_BLUE,
                "fg": PremiumTheme.TEXT_PRIMARY,
                "activebackground": "#005a9e",
                "activeforeground": PremiumTheme.TEXT_PRIMARY
            },
            "success": {
                "bg": PremiumTheme.ACCENT_GREEN,
                "fg": PremiumTheme.TEXT_PRIMARY,
                "activebackground": "#0e6e0e",
                "activeforeground": PremiumTheme.TEXT_PRIMARY
            },
            "danger": {
                "bg": PremiumTheme.ACCENT_RED,
                "fg": PremiumTheme.TEXT_PRIMARY,
                "activebackground": "#b02a2e",
                "activeforeground": PremiumTheme.TEXT_PRIMARY
            },
            "warning": {
                "bg": PremiumTheme.ACCENT_ORANGE,
                "fg": PremiumTheme.TEXT_PRIMARY,
                "activebackground": "#e67c00",
                "activeforeground": PremiumTheme.TEXT_PRIMARY
            }
        }

        button_style = styles.get(style, styles["primary"])

        # Merge all parameters
        button_params = {
            "text": text,
            "command": command,
            "font": PremiumTheme.FONT_MEDIUM,
            "relief": "flat",
            "borderwidth": 0,
            "cursor": "hand2",
            **button_style
        }

        # Override with any custom kwargs
        button_params.update(kwargs)

        super().__init__(parent, **button_params)

        # Add hover effects
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)

        self.original_bg = button_style["bg"]
        self.hover_bg = button_style["activebackground"]

    def _on_enter(self, event):
        """Handle mouse enter"""
        _ = event
        self.configure(bg=self.hover_bg)

    def _on_leave(self, event):
        """Handle mouse leave"""
        _ = event
        self.configure(bg=self.original_bg)

class StatusIndicator(tk.Frame):
    """Premium status indicator with color coding"""

    def __init__(self, parent, status="normal", size=12, **kwargs):
        super().__init__(parent, bg=PremiumTheme.DARK_BG, **kwargs)

        self.size = size
        self.canvas = tk.Canvas(
            self,
            width=size,
            height=size,
            bg=PremiumTheme.DARK_BG,
            highlightthickness=0
        )
        self.canvas.pack()

        self.set_status(status)

    def set_status(self, status):
        """Set status with beautiful color coding"""
        colors = {
            "normal": PremiumTheme.ACCENT_GREEN,      # Bright green for normal
            "warning": PremiumTheme.ACCENT_YELLOW,    # Bright yellow for warning
            "critical": PremiumTheme.ACCENT_RED,      # Bright red for critical
            "offline": PremiumTheme.TEXT_MUTED,       # Gray for offline
            "running": PremiumTheme.ACCENT_BLUE,      # Bright blue for running
            "success": PremiumTheme.ACCENT_GREEN,     # Green for success
            "error": PremiumTheme.ACCENT_RED          # Red for error
        }

        color = colors.get(status, PremiumTheme.TEXT_MUTED)

        self.canvas.delete("all")

        # Create a beautiful glowing effect
        # Outer glow
        self.canvas.create_oval(
            0, 0, self.size, self.size,
            fill="",
            outline=color,
            width=1
        )

        # Inner filled circle
        self.canvas.create_oval(
            2, 2, self.size-2, self.size-2,
            fill=color,
            outline="",
            width=0
        )

class PremiumProgressBar(tk.Frame):
    """Premium progress bar with gradient effect"""

    def __init__(self, parent, width=200, height=20, **kwargs):
        super().__init__(parent, bg=PremiumTheme.DARK_BG, **kwargs)

        self.width = width
        self.height = height
        self.progress = 0.0

        self.canvas = tk.Canvas(
            self,
            width=width,
            height=height,
            bg=PremiumTheme.LIGHT_BG,
            highlightthickness=1,
            highlightbackground=PremiumTheme.BORDER_COLOR
        )
        self.canvas.pack()

        self.update_progress(0)

    def update_progress(self, progress):
        """Update progress (0.0 to 1.0)"""
        self.progress = max(0.0, min(1.0, progress))

        self.canvas.delete("all")

        # Background
        self.canvas.create_rectangle(
            0, 0, self.width, self.height,
            fill=PremiumTheme.LIGHT_BG,
            outline=""
        )

        # Progress bar
        if self.progress > 0:
            progress_width = int(self.width * self.progress)
            self.canvas.create_rectangle(
                0, 0, progress_width, self.height,
                fill=PremiumTheme.ACCENT_BLUE,
                outline=""
            )

        # Progress text
        percentage = int(self.progress * 100)
        self.canvas.create_text(
            self.width // 2, self.height // 2,
            text=f"{percentage}%",
            fill=PremiumTheme.TEXT_PRIMARY,
            font=PremiumTheme.FONT_SMALL
        )

class PremiumCard(tk.Frame):
    """Beautiful premium card container with gradient and shadow effect"""

    def __init__(self, parent, title="", card_color=None, **kwargs):
        # Use beautiful card background
        bg_color = card_color or PremiumTheme.CARD_BG

        super().__init__(
            parent,
            bg=bg_color,
            relief="flat",
            borderwidth=0,
            highlightbackground=PremiumTheme.BORDER_COLOR,
            highlightthickness=1,
            **kwargs
        )

        # Beautiful title bar with gradient effect
        if title:
            title_frame = tk.Frame(
                self,
                bg=PremiumTheme.ACCENT_BLUE,
                height=40
            )
            title_frame.pack(fill=tk.X)
            title_frame.pack_propagate(False)

            # Title with icon
            title_label = tk.Label(
                title_frame,
                text=f"  {title}",  # Add some padding
                bg=PremiumTheme.ACCENT_BLUE,
                fg=PremiumTheme.TEXT_PRIMARY,
                font=("Segoe UI", 12, "bold"),
                anchor="w"
            )
            title_label.pack(side=tk.LEFT, padx=15, pady=8)

            # Add a subtle separator line
            separator = tk.Frame(
                self,
                bg=PremiumTheme.BORDER_COLOR,
                height=1
            )
            separator.pack(fill=tk.X)

        # Beautiful content area with padding
        self.content_frame = tk.Frame(
            self,
            bg=bg_color,
            padx=5,
            pady=5
        )
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

class ChannelWidget(PremiumCard):
    """Premium channel display widget"""

    def __init__(self, parent, channel_id, **kwargs):
        super().__init__(parent, title=f"Channel {channel_id}", **kwargs)

        self.channel_id = channel_id

        # Status indicator
        status_frame = tk.Frame(self.content_frame, bg=PremiumTheme.MEDIUM_BG)
        status_frame.pack(fill=tk.X, pady=(0, 5))

        tk.Label(
            status_frame,
            text="Status:",
            bg=PremiumTheme.MEDIUM_BG,
            fg=PremiumTheme.TEXT_SECONDARY,
            font=PremiumTheme.FONT_SMALL
        ).pack(side=tk.LEFT)

        self.status_indicator = StatusIndicator(status_frame, status="offline")
        self.status_indicator.pack(side=tk.LEFT, padx=(5, 0))

        self.status_label = tk.Label(
            status_frame,
            text="Offline",
            bg=PremiumTheme.MEDIUM_BG,
            fg=PremiumTheme.TEXT_SECONDARY,
            font=PremiumTheme.FONT_SMALL
        )
        self.status_label.pack(side=tk.LEFT, padx=(5, 0))

        # Beautiful measurements display
        self.voltage_var = tk.StringVar(value="-- V")
        self.current_var = tk.StringVar(value="-- A")
        self.temp_var = tk.StringVar(value="-- °C")
        self.soc_var = tk.StringVar(value="-- %")

        measurements = [
            ("⚡ Voltage:", self.voltage_var, PremiumTheme.ACCENT_BLUE),
            ("🔋 Current:", self.current_var, PremiumTheme.ACCENT_GREEN),
            ("🌡️ Temperature:", self.temp_var, PremiumTheme.ACCENT_ORANGE),
            ("📊 SOC:", self.soc_var, PremiumTheme.ACCENT_PURPLE)
        ]

        for label_text, var, color in measurements:
            # Create measurement frame with beautiful styling
            frame = tk.Frame(
                self.content_frame,
                bg=PremiumTheme.CARD_BG,
                relief="flat",
                borderwidth=1,
                highlightbackground=PremiumTheme.BORDER_COLOR,
                highlightthickness=1
            )
            frame.pack(fill=tk.X, pady=3, padx=2)

            # Label with icon
            tk.Label(
                frame,
                text=label_text,
                bg=PremiumTheme.CARD_BG,
                fg=PremiumTheme.TEXT_SECONDARY,
                font=("Segoe UI", 9),
                width=15,
                anchor="w"
            ).pack(side=tk.LEFT, padx=8, pady=4)

            # Value with bright color
            tk.Label(
                frame,
                textvariable=var,
                bg=PremiumTheme.CARD_BG,
                fg=color,
                font=("Segoe UI", 10, "bold"),
                anchor="e"
            ).pack(side=tk.RIGHT, padx=8, pady=4)

    def update_measurement(self, measurement: Measurement):
        """Update channel measurement display"""
        self.voltage_var.set(f"{measurement.voltage:.3f} V")
        self.current_var.set(f"{measurement.current:.3f} A")
        self.temp_var.set(f"{measurement.temperature:.1f} °C")
        self.soc_var.set(f"{measurement.soc:.1f} %")

    def update_status(self, status: TestStatus, safety_level: SafetyLevel):
        """Update channel status"""
        self.status_label.config(text=status.value)

        if status == TestStatus.RUNNING:
            indicator_status = "running"
        elif safety_level == SafetyLevel.CRITICAL:
            indicator_status = "critical"
        elif safety_level == SafetyLevel.WARNING:
            indicator_status = "warning"
        elif status == TestStatus.IDLE:
            indicator_status = "normal"
        else:
            indicator_status = "offline"

        self.status_indicator.set_status(indicator_status)

    def _start_data_acquisition(self):
        """Start continuous data acquisition"""
        if not self.data_acquisition_running:
            self.data_acquisition_running = True
            self.data_thread = threading.Thread(target=self._data_acquisition_loop, daemon=True)
            self.data_thread.start()
            logger.info("Data acquisition started")

    def _stop_data_acquisition(self):
        """Stop data acquisition"""
        self.data_acquisition_running = False
        if self.data_thread:
            self.data_thread.join(timeout=1.0)
        logger.info("Data acquisition stopped")

    def _data_acquisition_loop(self):
        """Main data acquisition loop"""
        while self.data_acquisition_running:
            try:
                for channel_id in range(self.hardware.get_channel_count()):
                    # Read measurement from hardware
                    measurement = self.hardware.read_measurement(channel_id)

                    # Update plot data
                    current_time = time.time()
                    self.plot_data[channel_id]['time'].append(current_time)
                    self.plot_data[channel_id]['voltage'].append(measurement.voltage)
                    self.plot_data[channel_id]['current'].append(measurement.current)
                    self.plot_data[channel_id]['temperature'].append(measurement.temperature)
                    self.plot_data[channel_id]['soc'].append(measurement.soc)

                    # Keep only last 100 points for performance
                    for key in self.plot_data[channel_id]:
                        if len(self.plot_data[channel_id][key]) > 100:
                            self.plot_data[channel_id][key] = self.plot_data[channel_id][key][-100:]

                    # Update channel widget if it exists
                    if channel_id in self.channel_widgets:
                        self.channel_widgets[channel_id].update_measurement(measurement)

                time.sleep(0.5)  # 2 Hz data acquisition

            except Exception as e:
                logger.error(f"Data acquisition error: {e}")
                time.sleep(1.0)

    def _start_gui_updates(self):
        """Start GUI update timer"""
        self._update_gui()

    def _update_gui(self):
        """Update GUI elements"""
        try:
            # Update plots if monitoring tab is visible
            if hasattr(self, 'canvas') and self.data_acquisition_running:
                self._update_plots()

        except Exception as e:
            logger.error(f"GUI update error: {e}")

        # Schedule next update
        self.root.after(1000, self._update_gui)  # Update every 1 second

    def _update_plots(self):
        """Update real-time plots"""
        try:
            if not hasattr(self, 'ax1'):
                return

            # Clear all plots
            self.ax1.clear()
            self.ax2.clear()
            self.ax3.clear()
            self.ax4.clear()

            # Plot data for selected channel
            channel_data = self.plot_data[self.selected_channel]

            if len(channel_data['time']) > 1:
                # Convert timestamps to relative time
                start_time = channel_data['time'][0]
                times = [(t - start_time) for t in channel_data['time']]

                # Plot with beautiful colors
                self.ax1.plot(times, channel_data['voltage'], color=PremiumTheme.ACCENT_BLUE, linewidth=2)
                self.ax2.plot(times, channel_data['current'], color=PremiumTheme.ACCENT_GREEN, linewidth=2)
                self.ax3.plot(times, channel_data['temperature'], color=PremiumTheme.ACCENT_ORANGE, linewidth=2)
                self.ax4.plot(times, channel_data['soc'], color=PremiumTheme.ACCENT_PURPLE, linewidth=2)

            # Configure plot appearance
            plots = [
                (self.ax1, 'Voltage vs Time', 'Time (s)', 'Voltage (V)'),
                (self.ax2, 'Current vs Time', 'Time (s)', 'Current (A)'),
                (self.ax3, 'Temperature vs Time', 'Time (s)', 'Temperature (°C)'),
                (self.ax4, 'State of Charge', 'Time (s)', 'SOC (%)')
            ]

            for ax, title, xlabel, ylabel in plots:
                ax.set_title(title, color=PremiumTheme.TEXT_PRIMARY, fontsize=12, fontweight='bold')
                ax.set_xlabel(xlabel, color=PremiumTheme.TEXT_PRIMARY, fontsize=10)
                ax.set_ylabel(ylabel, color=PremiumTheme.TEXT_PRIMARY, fontsize=10)
                ax.tick_params(colors=PremiumTheme.TEXT_PRIMARY, labelsize=8)
                ax.grid(True, alpha=0.3, color=PremiumTheme.TEXT_MUTED)
                ax.set_facecolor(PremiumTheme.LIGHT_BG)

                # Style spines
                for spine in ax.spines.values():
                    spine.set_color(PremiumTheme.TEXT_MUTED)

            self.fig.tight_layout()
            self.canvas.draw()

        except Exception as e:
            logger.error(f"Plot update error: {e}")

# ============================================================================
# PREMIUM MAIN APPLICATION
# ============================================================================

class PremiumBatteryTestingGUI:
    """Premium Battery Testing GUI Application"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Premium Battery Testing Software v2.0 - Professional Edition")
        self.root.geometry("1600x1000")
        self.root.configure(bg=PremiumTheme.DARK_BG)
        self.root.state('zoomed')  # Maximize window on Windows

        # Set window icon (if available)
        try:
            self.root.iconbitmap('battery_icon.ico')
        except:
            pass

        # Initialize backend components
        self.hardware_config = HardwareConfig(
            protocol=HardwareProtocol.MOCK,
            connection_string="mock://localhost"
        )

        self.hardware = HardwareDriverFactory.create_driver(self.hardware_config)

        # Initialize other components
        self.selected_channel = 0
        self.channel_widgets = {}
        self.data_acquisition_running = False
        self.data_thread = None
        self.plot_data = {i: {'time': [], 'voltage': [], 'current': [], 'temperature': [], 'soc': []}
                         for i in range(self.hardware.get_channel_count())}

        # Configure premium styling
        self.style = ttk.Style()
        PremiumTheme.configure_styles(self.style)

        # Setup GUI
        self._setup_gui()
        self._setup_menu()

        # Connect hardware
        self.hardware.connect()

    def _setup_gui(self):
        """Setup the premium GUI layout"""

        # Main container with gradient background
        self.main_frame = tk.Frame(self.root, bg=PremiumTheme.DARK_BG)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # Top header bar
        self._create_header()

        # Main content area
        content_frame = tk.Frame(self.main_frame, bg=PremiumTheme.DARK_BG)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Create notebook for tabbed interface
        self.notebook = ttk.Notebook(content_frame, style='Premium.TNotebook')
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Create tabs
        self._create_dashboard_tab()
        self._create_testing_tab()
        self._create_monitoring_tab()
        self._create_data_tab()
        self._create_settings_tab()

        # Status bar
        self._create_status_bar()

    def _create_header(self):
        """Create beautiful premium header with gradient and branding"""
        # Create gradient header frame
        header_frame = tk.Frame(
            self.main_frame,
            bg=PremiumTheme.ACCENT_BLUE,
            height=90
        )
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # Logo and title section
        title_frame = tk.Frame(header_frame, bg=PremiumTheme.ACCENT_BLUE)
        title_frame.pack(side=tk.LEFT, fill=tk.Y, padx=25)

        # Beautiful main title with icon
        title_label = tk.Label(
            title_frame,
            text="🔋 Premium Battery Testing Software",
            bg=PremiumTheme.ACCENT_BLUE,
            fg=PremiumTheme.TEXT_PRIMARY,
            font=("Segoe UI", 20, "bold")
        )
        title_label.pack(anchor="w", pady=(15, 2))

        # Attractive subtitle
        subtitle_label = tk.Label(
            title_frame,
            text="✨ Professional Edition v2.0 - Multi-Protocol Support ✨",
            bg=PremiumTheme.ACCENT_BLUE,
            fg=PremiumTheme.TEXT_PRIMARY,
            font=("Segoe UI", 11)
        )
        subtitle_label.pack(anchor="w")

        # Beautiful control buttons section
        controls_frame = tk.Frame(header_frame, bg=PremiumTheme.ACCENT_BLUE)
        controls_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=25)

        # Status section
        status_frame = tk.Frame(controls_frame, bg=PremiumTheme.ACCENT_BLUE)
        status_frame.pack(side=tk.RIGHT, padx=(0, 20), pady=20)

        # Connection status with beautiful indicator
        connection_frame = tk.Frame(status_frame, bg=PremiumTheme.ACCENT_BLUE)
        connection_frame.pack(anchor="e")

        self.connection_status = StatusIndicator(connection_frame, status="normal", size=20)
        self.connection_status.pack(side=tk.LEFT, padx=(0, 8))

        connection_label = tk.Label(
            connection_frame,
            text="🔗 Hardware Connected",
            bg=PremiumTheme.ACCENT_BLUE,
            fg=PremiumTheme.TEXT_PRIMARY,
            font=("Segoe UI", 11, "bold")
        )
        connection_label.pack(side=tk.LEFT)

        # Beautiful emergency stop button
        self.emergency_btn = PremiumButton(
            controls_frame,
            text="🚨 EMERGENCY STOP",
            style="danger",
            font=("Segoe UI", 13, "bold"),
            width=18,
            command=self._emergency_stop
        )
        self.emergency_btn.pack(side=tk.RIGHT, padx=10, pady=20)

    def _create_dashboard_tab(self):
        """Create dashboard overview tab"""
        dashboard_frame = ttk.Frame(self.notebook, style='Premium.TFrame')
        self.notebook.add(dashboard_frame, text="📊 Dashboard")

        # Create scrollable frame for channels
        canvas = tk.Canvas(dashboard_frame, bg=PremiumTheme.DARK_BG)
        scrollbar = ttk.Scrollbar(dashboard_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas, style='Premium.TFrame')

        scrollable_frame.bind(
            "<Configure>",
            lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack scrollable components
        canvas.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y")

        # Create channel widgets in grid
        channels_per_row = 4
        for i in range(self.hardware.get_channel_count()):
            row = i // channels_per_row
            col = i % channels_per_row

            channel_widget = ChannelWidget(scrollable_frame, i, width=300, height=200)
            channel_widget.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

            self.channel_widgets[i] = channel_widget

        # Configure grid weights
        for i in range(channels_per_row):
            scrollable_frame.columnconfigure(i, weight=1)

    def _create_testing_tab(self):
        """Create test configuration tab"""
        testing_frame = ttk.Frame(self.notebook, style='Premium.TFrame')
        self.notebook.add(testing_frame, text="🧪 Testing")

        # Left panel - Test configuration
        left_panel = PremiumCard(testing_frame, title="Test Configuration")
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(10, 5), pady=10, ipadx=10)

        # Channel selection
        tk.Label(
            left_panel.content_frame,
            text="Select Channel:",
            bg=PremiumTheme.MEDIUM_BG,
            fg=PremiumTheme.TEXT_PRIMARY,
            font=PremiumTheme.FONT_MEDIUM
        ).pack(anchor="w", pady=(0, 5))

        self.channel_var = tk.StringVar(value="Channel 0")
        channel_combo = ttk.Combobox(
            left_panel.content_frame,
            textvariable=self.channel_var,
            values=[f"Channel {i}" for i in range(self.hardware.get_channel_count())],
            state="readonly",
            style='Premium.TCombobox',
            width=20
        )
        channel_combo.pack(anchor="w", pady=(0, 10))

        # Test profile selection
        tk.Label(
            left_panel.content_frame,
            text="Test Profile:",
            bg=PremiumTheme.MEDIUM_BG,
            fg=PremiumTheme.TEXT_PRIMARY,
            font=PremiumTheme.FONT_MEDIUM
        ).pack(anchor="w", pady=(0, 5))

        self.profile_var = tk.StringVar(value=TestProfile.CC_CV.value)
        profile_combo = ttk.Combobox(
            left_panel.content_frame,
            textvariable=self.profile_var,
            values=[p.value for p in TestProfile],
            state="readonly",
            style='Premium.TCombobox',
            width=30
        )
        profile_combo.pack(anchor="w", pady=(0, 10))

        # Test parameters
        params_frame = tk.Frame(left_panel.content_frame, bg=PremiumTheme.MEDIUM_BG)
        params_frame.pack(fill=tk.X, pady=10)

        # Parameter entries
        self.voltage_var = tk.StringVar(value="3.7")
        self.current_var = tk.StringVar(value="1.0")
        self.duration_var = tk.StringVar(value="60")

        parameters = [
            ("Target Voltage (V):", self.voltage_var),
            ("Target Current (A):", self.current_var),
            ("Duration (min):", self.duration_var)
        ]

        for i, (label_text, var) in enumerate(parameters):
            tk.Label(
                params_frame,
                text=label_text,
                bg=PremiumTheme.MEDIUM_BG,
                fg=PremiumTheme.TEXT_PRIMARY,
                font=PremiumTheme.FONT_MEDIUM
            ).grid(row=i, column=0, sticky="w", pady=5)

            entry = ttk.Entry(
                params_frame,
                textvariable=var,
                style='Premium.TEntry',
                width=15
            )
            entry.grid(row=i, column=1, padx=(10, 0), pady=5)

        # Control buttons
        button_frame = tk.Frame(left_panel.content_frame, bg=PremiumTheme.MEDIUM_BG)
        button_frame.pack(fill=tk.X, pady=20)

        self.start_btn = PremiumButton(
            button_frame,
            text="▶ Start Test",
            style="success",
            command=self._start_test
        )
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.stop_btn = PremiumButton(
            button_frame,
            text="⏹ Stop Test",
            style="danger",
            command=self._stop_test
        )
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.pause_btn = PremiumButton(
            button_frame,
            text="⏸ Pause",
            style="warning",
            command=self._pause_test
        )
        self.pause_btn.pack(side=tk.LEFT)

        # Right panel - Test progress
        right_panel = PremiumCard(testing_frame, title="Test Progress")
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 10), pady=10)

        # Progress bar
        self.progress_bar = PremiumProgressBar(right_panel.content_frame, width=400, height=30)
        self.progress_bar.pack(pady=10)

        # Test information
        self.test_info_text = tk.Text(
            right_panel.content_frame,
            bg=PremiumTheme.LIGHT_BG,
            fg=PremiumTheme.TEXT_PRIMARY,
            font=PremiumTheme.FONT_MONO,
            height=15,
            wrap=tk.WORD
        )
        self.test_info_text.pack(fill=tk.BOTH, expand=True, pady=10)

        # Add scrollbar to text widget
        info_scrollbar = ttk.Scrollbar(right_panel.content_frame, command=self.test_info_text.yview)
        self.test_info_text.configure(yscrollcommand=info_scrollbar.set)

    def _create_monitoring_tab(self):
        """Create real-time monitoring tab"""
        monitoring_frame = ttk.Frame(self.notebook, style='Premium.TFrame')
        self.notebook.add(monitoring_frame, text="📈 Monitoring")

        # Create matplotlib figure with premium styling
        self.fig = Figure(figsize=(12, 8), facecolor=PremiumTheme.DARK_BG)
        self.fig.patch.set_facecolor(PremiumTheme.DARK_BG)

        # Create subplots with premium styling
        self.ax1 = self.fig.add_subplot(221, facecolor=PremiumTheme.LIGHT_BG)
        self.ax2 = self.fig.add_subplot(222, facecolor=PremiumTheme.LIGHT_BG)
        self.ax3 = self.fig.add_subplot(223, facecolor=PremiumTheme.LIGHT_BG)
        self.ax4 = self.fig.add_subplot(224, facecolor=PremiumTheme.LIGHT_BG)

        # Configure subplot appearance
        plots = [
            (self.ax1, 'Voltage vs Time', 'Time (s)', 'Voltage (V)', PremiumTheme.ACCENT_BLUE),
            (self.ax2, 'Current vs Time', 'Time (s)', 'Current (A)', PremiumTheme.ACCENT_GREEN),
            (self.ax3, 'Temperature vs Time', 'Time (s)', 'Temperature (°C)', PremiumTheme.ACCENT_ORANGE),
            (self.ax4, 'State of Charge', 'Time (s)', 'SOC (%)', PremiumTheme.ACCENT_PURPLE)
        ]

        for ax, title, xlabel, ylabel, _ in plots:
            ax.set_title(title, color=PremiumTheme.TEXT_PRIMARY, fontsize=12, fontweight='bold')
            ax.set_xlabel(xlabel, color=PremiumTheme.TEXT_PRIMARY, fontsize=10)
            ax.set_ylabel(ylabel, color=PremiumTheme.TEXT_PRIMARY, fontsize=10)
            ax.tick_params(colors=PremiumTheme.TEXT_PRIMARY, labelsize=8)
            ax.grid(True, alpha=0.3, color=PremiumTheme.TEXT_MUTED)

            # Style spines
            for spine in ax.spines.values():
                spine.set_color(PremiumTheme.TEXT_MUTED)

        self.fig.tight_layout()

        # Create canvas
        self.canvas = FigureCanvasTkAgg(self.fig, monitoring_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    def _create_data_tab(self):
        """Create data management tab"""
        data_frame = ttk.Frame(self.notebook, style='Premium.TFrame')
        self.notebook.add(data_frame, text="💾 Data")

        # Data export section
        export_card = PremiumCard(data_frame, title="Data Export")
        export_card.pack(fill=tk.X, padx=10, pady=10)

        export_buttons_frame = tk.Frame(export_card.content_frame, bg=PremiumTheme.MEDIUM_BG)
        export_buttons_frame.pack(fill=tk.X, pady=10)

        PremiumButton(
            export_buttons_frame,
            text="📄 Export CSV",
            style="primary",
            command=self._export_csv
        ).pack(side=tk.LEFT, padx=(0, 10))

        PremiumButton(
            export_buttons_frame,
            text="📊 Export Excel",
            style="primary",
            command=self._export_excel
        ).pack(side=tk.LEFT, padx=(0, 10))

        PremiumButton(
            export_buttons_frame,
            text="📋 Generate Report",
            style="primary",
            command=self._generate_report
        ).pack(side=tk.LEFT)

    def _create_settings_tab(self):
        """Create settings and configuration tab"""
        settings_frame = ttk.Frame(self.notebook, style='Premium.TFrame')
        self.notebook.add(settings_frame, text="⚙️ Settings")

        # Hardware configuration
        hw_card = PremiumCard(settings_frame, title="Hardware Configuration")
        hw_card.pack(fill=tk.X, padx=10, pady=10)

        # Protocol selection
        tk.Label(
            hw_card.content_frame,
            text="Communication Protocol:",
            bg=PremiumTheme.MEDIUM_BG,
            fg=PremiumTheme.TEXT_PRIMARY,
            font=PremiumTheme.FONT_MEDIUM
        ).pack(anchor="w", pady=(0, 5))

        self.protocol_var = tk.StringVar(value=self.hardware_config.protocol.value)
        protocol_combo = ttk.Combobox(
            hw_card.content_frame,
            textvariable=self.protocol_var,
            values=[p.value for p in HardwareDriverFactory.get_available_protocols()],
            state="readonly",
            style='Premium.TCombobox',
            width=30
        )
        protocol_combo.pack(anchor="w", pady=(0, 10))

        # Connection settings
        conn_frame = tk.Frame(hw_card.content_frame, bg=PremiumTheme.MEDIUM_BG)
        conn_frame.pack(fill=tk.X, pady=10)

        # Add connection parameter fields
        self.ip_var = tk.StringVar(value="*************")
        self.port_var = tk.StringVar(value="502")
        self.baudrate_var = tk.StringVar(value="9600")

        connection_params = [
            ("IP Address:", self.ip_var),
            ("Port:", self.port_var),
            ("Baudrate:", self.baudrate_var)
        ]

        for i, (label_text, var) in enumerate(connection_params):
            tk.Label(
                conn_frame,
                text=label_text,
                bg=PremiumTheme.MEDIUM_BG,
                fg=PremiumTheme.TEXT_PRIMARY,
                font=PremiumTheme.FONT_MEDIUM
            ).grid(row=i, column=0, sticky="w", pady=5)

            entry = ttk.Entry(
                conn_frame,
                textvariable=var,
                style='Premium.TEntry',
                width=20
            )
            entry.grid(row=i, column=1, padx=(10, 0), pady=5)

        # Apply settings button
        PremiumButton(
            hw_card.content_frame,
            text="Apply Settings",
            style="success",
            command=self._apply_hardware_settings
        ).pack(anchor="w", pady=10)

    def _create_status_bar(self):
        """Create status bar"""
        status_frame = tk.Frame(self.main_frame, bg=PremiumTheme.MEDIUM_BG, height=30)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(
            status_frame,
            text="Ready - Premium Battery Testing Software",
            bg=PremiumTheme.MEDIUM_BG,
            fg=PremiumTheme.TEXT_PRIMARY,
            font=PremiumTheme.FONT_SMALL,
            anchor="w"
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)

        # Connection info
        self.connection_info = tk.Label(
            status_frame,
            text=f"Connected: {self.hardware_config.protocol.value}",
            bg=PremiumTheme.MEDIUM_BG,
            fg=PremiumTheme.TEXT_SECONDARY,
            font=PremiumTheme.FONT_SMALL
        )
        self.connection_info.pack(side=tk.RIGHT, padx=10, pady=5)

    def _setup_menu(self):
        """Setup application menu"""
        menubar = tk.Menu(self.root, bg=PremiumTheme.MEDIUM_BG, fg=PremiumTheme.TEXT_PRIMARY)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0, bg=PremiumTheme.MEDIUM_BG, fg=PremiumTheme.TEXT_PRIMARY)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Test Session", command=self._new_session)
        file_menu.add_command(label="Open Session", command=self._open_session)
        file_menu.add_command(label="Save Session", command=self._save_session)
        file_menu.add_separator()
        file_menu.add_command(label="Export Data", command=self._export_csv)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)

        # Hardware menu
        hardware_menu = tk.Menu(menubar, tearoff=0, bg=PremiumTheme.MEDIUM_BG, fg=PremiumTheme.TEXT_PRIMARY)
        menubar.add_cascade(label="Hardware", menu=hardware_menu)
        hardware_menu.add_command(label="Connect", command=self._connect_hardware)
        hardware_menu.add_command(label="Disconnect", command=self._disconnect_hardware)
        hardware_menu.add_command(label="Calibrate All", command=self._calibrate_all)
        hardware_menu.add_command(label="Device Info", command=self._show_device_info)

        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0, bg=PremiumTheme.MEDIUM_BG, fg=PremiumTheme.TEXT_PRIMARY)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Data Analysis", command=self._show_analysis)
        tools_menu.add_command(label="Report Generator", command=self._generate_report)
        tools_menu.add_command(label="System Monitor", command=self._show_system_monitor)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0, bg=PremiumTheme.MEDIUM_BG, fg=PremiumTheme.TEXT_PRIMARY)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="User Manual", command=self._show_manual)
        help_menu.add_command(label="Hardware Protocols", command=self._show_protocols)
        help_menu.add_command(label="About", command=self._show_about)

    # Event Handlers
    def _emergency_stop(self):
        """Emergency stop all operations"""
        try:
            # Stop all channels
            for i in range(self.hardware.get_channel_count()):
                self.hardware.set_output(i, 0.0, 0.0)

            self.status_label.config(text="EMERGENCY STOP ACTIVATED", fg=PremiumTheme.ACCENT_RED)

            # Show emergency dialog
            from tkinter import messagebox
            messagebox.showerror(
                "Emergency Stop",
                "Emergency stop activated!\nAll channels have been shut down.",
                icon='error'
            )

        except Exception as e:
            logger.error(f"Emergency stop error: {e}")

    def _start_test(self):
        """Start test on selected channel with beautiful feedback"""
        try:
            channel_text = self.channel_var.get()
            channel_id = int(channel_text.split()[1])

            # Create test configuration
            config = TestConfiguration(
                profile=TestProfile(self.profile_var.get()),
                channel_id=channel_id,
                duration=int(float(self.duration_var.get()) * 60),
                target_voltage=float(self.voltage_var.get()),
                target_current=float(self.current_var.get())
            )

            # Start test
            success = self.hardware.set_output(channel_id, config.target_voltage, config.target_current)

            if success:
                # Update UI with beautiful formatting
                self.test_info_text.insert(tk.END, f"✅ Started {config.profile.value} test on Channel {channel_id}\n")
                self.test_info_text.insert(tk.END, f"🎯 Target: {config.target_voltage}V, {config.target_current}A\n")
                self.test_info_text.insert(tk.END, f"⏱️ Duration: {config.duration//60} minutes\n")
                self.test_info_text.insert(tk.END, f"🕐 Started at: {datetime.now().strftime('%H:%M:%S')}\n\n")
                self.test_info_text.see(tk.END)

                # Update status with success color
                self.status_label.config(
                    text=f"✅ Test running on Channel {channel_id}",
                    fg=PremiumTheme.TEXT_SUCCESS
                )

                # Update progress bar
                self.progress_bar.update_progress(0.1)

                # Update channel widget status
                if channel_id in self.channel_widgets:
                    self.channel_widgets[channel_id].update_status(TestStatus.RUNNING, SafetyLevel.NORMAL)

            else:
                self.test_info_text.insert(tk.END, f"❌ Failed to start test on Channel {channel_id}\n\n")
                self.test_info_text.see(tk.END)

        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror("Test Error", f"Failed to start test: {e}")
            self.test_info_text.insert(tk.END, f"❌ Error: {e}\n\n")
            self.test_info_text.see(tk.END)

    def _stop_test(self):
        """Stop test on selected channel with beautiful feedback"""
        try:
            channel_text = self.channel_var.get()
            channel_id = int(channel_text.split()[1])

            # Stop hardware output
            success = self.hardware.set_output(channel_id, 0.0, 0.0)

            if success:
                # Update UI with beautiful formatting
                self.test_info_text.insert(tk.END, f"⏹️ Stopped test on Channel {channel_id}\n")
                self.test_info_text.insert(tk.END, f"🕐 Stopped at: {datetime.now().strftime('%H:%M:%S')}\n\n")
                self.test_info_text.see(tk.END)

                # Update status
                self.status_label.config(
                    text="⏹️ Test stopped",
                    fg=PremiumTheme.TEXT_WARNING
                )

                # Reset progress bar
                self.progress_bar.update_progress(0.0)

                # Update channel widget status
                if channel_id in self.channel_widgets:
                    self.channel_widgets[channel_id].update_status(TestStatus.IDLE, SafetyLevel.NORMAL)

            else:
                self.test_info_text.insert(tk.END, f"❌ Failed to stop test on Channel {channel_id}\n\n")
                self.test_info_text.see(tk.END)

        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror("Stop Error", f"Failed to stop test: {e}")
            self.test_info_text.insert(tk.END, f"❌ Error: {e}\n\n")
            self.test_info_text.see(tk.END)

    def _pause_test(self):
        """Pause/resume test with beautiful feedback"""
        try:
            channel_text = self.channel_var.get()
            channel_id = int(channel_text.split()[1])

            # Toggle pause state
            if self.pause_btn.cget('text') == "⏸ Pause":
                # Pause the test
                self.test_info_text.insert(tk.END, f"⏸️ Paused test on Channel {channel_id}\n")
                self.test_info_text.insert(tk.END, f"🕐 Paused at: {datetime.now().strftime('%H:%M:%S')}\n\n")
                self.pause_btn.config(text="▶ Resume", bg=PremiumTheme.ACCENT_GREEN)

                # Update status
                self.status_label.config(
                    text=f"⏸️ Test paused on Channel {channel_id}",
                    fg=PremiumTheme.TEXT_WARNING
                )

                # Update channel widget status
                if channel_id in self.channel_widgets:
                    self.channel_widgets[channel_id].update_status(TestStatus.PAUSED, SafetyLevel.NORMAL)

            else:
                # Resume the test
                self.test_info_text.insert(tk.END, f"▶️ Resumed test on Channel {channel_id}\n")
                self.test_info_text.insert(tk.END, f"🕐 Resumed at: {datetime.now().strftime('%H:%M:%S')}\n\n")
                self.pause_btn.config(text="⏸ Pause", bg=PremiumTheme.ACCENT_ORANGE)

                # Update status
                self.status_label.config(
                    text=f"▶️ Test resumed on Channel {channel_id}",
                    fg=PremiumTheme.TEXT_SUCCESS
                )

                # Update channel widget status
                if channel_id in self.channel_widgets:
                    self.channel_widgets[channel_id].update_status(TestStatus.RUNNING, SafetyLevel.NORMAL)

            self.test_info_text.see(tk.END)

        except Exception as e:
            self.test_info_text.insert(tk.END, f"❌ Pause/Resume Error: {e}\n\n")
            self.test_info_text.see(tk.END)

    def _apply_hardware_settings(self):
        """Apply hardware configuration settings"""
        try:
            # Update hardware configuration
            protocol_name = self.protocol_var.get()
            protocol = next(p for p in HardwareProtocol if p.value == protocol_name)

            new_config = HardwareConfig(
                protocol=protocol,
                connection_string=f"{self.ip_var.get()}:{self.port_var.get()}",
                ip_address=self.ip_var.get(),
                port=int(self.port_var.get()),
                baudrate=int(self.baudrate_var.get())
            )

            # Disconnect current hardware
            self.hardware.disconnect()

            # Create new hardware driver
            self.hardware = HardwareDriverFactory.create_driver(new_config)
            self.hardware_config = new_config

            # Connect new hardware
            if self.hardware.connect():
                self.connection_info.config(text=f"Connected: {protocol.value}")
                self.connection_status.set_status("normal")
                self.status_label.config(text="Hardware settings applied successfully")
            else:
                self.connection_status.set_status("critical")
                self.status_label.config(text="Failed to connect with new settings")

        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror("Error", f"Failed to apply settings: {e}")

    # Menu handlers (simplified implementations)
    def _new_session(self):
        """Create new test session"""
        self.test_info_text.delete(1.0, tk.END)
        self.test_info_text.insert(tk.END, "New test session created\n\n")

    def _open_session(self):
        """Open existing test session"""
        from tkinter import filedialog
        filename = filedialog.askopenfilename(
            title="Open Test Session",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            self.status_label.config(text=f"Opened session: {filename}")

    def _save_session(self):
        """Save current test session"""
        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            title="Save Test Session",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            self.status_label.config(text=f"Saved session: {filename}")

    def _connect_hardware(self):
        """Connect to hardware"""
        if self.hardware.connect():
            self.connection_status.set_status("normal")
            self.status_label.config(text="Hardware connected")
        else:
            self.connection_status.set_status("critical")
            self.status_label.config(text="Hardware connection failed")

    def _disconnect_hardware(self):
        """Disconnect from hardware"""
        self.hardware.disconnect()
        self.connection_status.set_status("offline")
        self.status_label.config(text="Hardware disconnected")

    def _calibrate_all(self):
        """Calibrate all channels"""
        self.status_label.config(text="Calibrating all channels...")
        self.root.update()

        for i in range(self.hardware.get_channel_count()):
            self.hardware.calibrate_channel(i)

        self.status_label.config(text="Calibration completed")

    def _show_device_info(self):
        """Show device information"""
        info = self.hardware.get_device_info()
        info_text = "\n".join([f"{k}: {v}" for k, v in info.items()])

        from tkinter import messagebox
        messagebox.showinfo("Device Information", info_text)

    def _export_csv(self):
        """Export data to CSV"""
        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            title="Export CSV Data",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            self.status_label.config(text=f"Data exported to {filename}")

    def _export_excel(self):
        """Export data to Excel"""
        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            title="Export Excel Data",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if filename:
            self.status_label.config(text=f"Data exported to {filename}")

    def _generate_report(self):
        """Generate test report"""
        self.status_label.config(text="Generating report...")
        # Simulate report generation
        self.root.after(2000, lambda: self.status_label.config(text="Report generated successfully"))

    def _show_analysis(self):
        """Show data analysis window"""
        from tkinter import messagebox
        messagebox.showinfo("Data Analysis", "Data analysis feature coming soon!")

    def _show_system_monitor(self):
        """Show system monitor"""
        from tkinter import messagebox
        messagebox.showinfo("System Monitor", "System monitoring feature coming soon!")

    def _show_manual(self):
        """Show user manual"""
        manual_window = tk.Toplevel(self.root)
        manual_window.title("User Manual")
        manual_window.geometry("800x600")
        manual_window.configure(bg=PremiumTheme.DARK_BG)

        manual_text = tk.Text(
            manual_window,
            bg=PremiumTheme.LIGHT_BG,
            fg=PremiumTheme.TEXT_PRIMARY,
            font=PremiumTheme.FONT_MEDIUM,
            wrap=tk.WORD
        )
        manual_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        manual_content = """
PREMIUM BATTERY TESTING SOFTWARE - USER MANUAL

1. GETTING STARTED
   - Connect your battery testing hardware
   - Select appropriate communication protocol in Settings
   - Configure connection parameters
   - Click "Apply Settings" to establish connection

2. HARDWARE PROTOCOLS SUPPORTED
   - Mock Hardware (for demonstration)
   - Modbus TCP/RTU
   - CANbus
   - Serial RS232/RS485
   - USB HID/Serial
   - Ethernet TCP/UDP
   - VISA Instruments

3. RUNNING TESTS
   - Go to Testing tab
   - Select channel and test profile
   - Configure test parameters
   - Click "Start Test"
   - Monitor progress in real-time

4. MONITORING
   - Real-time plots show voltage, current, temperature, SOC
   - Dashboard provides overview of all channels
   - Status indicators show channel health

5. DATA MANAGEMENT
   - Export data in CSV or Excel format
   - Generate professional reports
   - Automatic data logging to database

6. SAFETY FEATURES
   - Emergency stop button
   - Configurable safety limits
   - Automatic protection systems
   - Audit trail logging

For technical support, contact: <EMAIL>
        """

        manual_text.insert(tk.END, manual_content)
        manual_text.config(state=tk.DISABLED)

    def _show_protocols(self):
        """Show hardware protocols information"""
        protocols_window = tk.Toplevel(self.root)
        protocols_window.title("Hardware Protocols")
        protocols_window.geometry("600x500")
        protocols_window.configure(bg=PremiumTheme.DARK_BG)

        protocols_text = tk.Text(
            protocols_window,
            bg=PremiumTheme.LIGHT_BG,
            fg=PremiumTheme.TEXT_PRIMARY,
            font=PremiumTheme.FONT_MEDIUM,
            wrap=tk.WORD
        )
        protocols_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        protocols_content = """
SUPPORTED HARDWARE PROTOCOLS

1. MODBUS TCP
   - IP-based communication
   - Standard port 502
   - Supports multiple devices
   - Configuration: IP address, port, device ID

2. MODBUS RTU
   - Serial communication
   - RS485 network support
   - Configuration: COM port, baudrate, device ID

3. CANbus
   - Automotive standard
   - High-speed communication
   - Configuration: CAN channel, bitrate

4. Serial RS232/RS485
   - Direct serial communication
   - SCPI command support
   - Configuration: COM port, baudrate

5. USB HID/Serial
   - Plug-and-play USB devices
   - Automatic device detection
   - Configuration: Device selection

6. Ethernet TCP/UDP
   - Network-based instruments
   - Custom protocol support
   - Configuration: IP, port, protocol type

7. VISA Instruments
   - Industry standard
   - Supports multiple vendors
   - Configuration: VISA resource string

CONNECTION EXAMPLES:
- Modbus TCP: *************:502
- Serial: COM3:9600
- CANbus: can0:500000
- USB: USB0::0x1234::0x5678::INSTR
        """

        protocols_text.insert(tk.END, protocols_content)
        protocols_text.config(state=tk.DISABLED)

    def _show_about(self):
        """Show about dialog"""
        from tkinter import messagebox
        about_text = """
Premium Battery Testing Software v2.0
Professional Edition

A comprehensive battery testing solution with:
• Multi-protocol hardware support
• Real-time monitoring and control
• Advanced safety systems
• Professional reporting
• Data analytics capabilities

Supported Protocols:
• Modbus TCP/RTU
• CANbus
• Serial RS232/RS485
• USB HID/Serial
• Ethernet TCP/UDP
• VISA Instruments

© 2024 Premium Battery Systems
All rights reserved.
        """

        messagebox.showinfo("About", about_text)

    def run(self):
        """Start the premium GUI application"""
        try:
            # Start data acquisition
            self._start_data_acquisition()

            # Start GUI updates
            self._start_gui_updates()

            self.status_label.config(text="Premium Battery Testing Software ready")
            self.root.mainloop()

        except KeyboardInterrupt:
            logger.info("Application interrupted by user")
        except Exception as e:
            logger.error(f"Application error: {e}")
            from tkinter import messagebox
            messagebox.showerror("Application Error", f"An error occurred: {e}")
        finally:
            self._cleanup()

    def _cleanup(self):
        """Cleanup resources before exit"""
        try:
            # Stop data acquisition
            self._stop_data_acquisition()

            # Disconnect hardware
            self.hardware.disconnect()

            logger.info("Application cleanup completed")
        except Exception as e:
            logger.error(f"Cleanup error: {e}")

# ============================================================================
# MAIN APPLICATION ENTRY POINT
# ============================================================================

def main():
    """Main application entry point"""
    try:
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('premium_battery_testing.log'),
                logging.StreamHandler()
            ]
        )

        logger.info("Starting Premium Battery Testing Software v2.0")

        # Create and run premium GUI application
        app = PremiumBatteryTestingGUI()
        app.run()

    except Exception as e:
        logger.critical(f"Failed to start application: {e}")
        print(f"Critical error: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
