Metadata-Version: 2.4
Name: battery-testing-software
Version: 1.0.0
Summary: Comprehensive battery testing software with clean Tkinter UI
Home-page: https://github.com/batterytesting/battery-testing-software
Author: Battery Testing Solutions
Author-email: Battery Testing Solutions <<EMAIL>>
Maintainer-email: Battery Testing Solutions <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/batterytesting/battery-testing-software
Project-URL: Documentation, https://battery-testing-software.readthedocs.io/
Project-URL: Repository, https://github.com/batterytesting/battery-testing-software
Project-URL: Bug Tracker, https://github.com/batterytesting/battery-testing-software/issues
Keywords: battery testing,electrochemical testing,battery cycler,data acquisition,laboratory automation
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: Manufacturing
Classifier: Topic :: Scientific/Engineering :: Chemistry
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Operating System :: OS Independent
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: numpy>=1.21.0
Requires-Dist: matplotlib>=3.5.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: pytest-mock>=3.8.0; extra == "dev"
Requires-Dist: black>=22.0.0; extra == "dev"
Requires-Dist: flake8>=4.0.0; extra == "dev"
Requires-Dist: mypy>=0.950; extra == "dev"
Provides-Extra: postgresql
Requires-Dist: psycopg2-binary>=2.9.0; extra == "postgresql"
Provides-Extra: mysql
Requires-Dist: PyMySQL>=1.0.0; extra == "mysql"
Provides-Extra: influxdb
Requires-Dist: influxdb-client>=1.30.0; extra == "influxdb"
Provides-Extra: hardware
Requires-Dist: pyserial>=3.5; extra == "hardware"
Requires-Dist: pymodbus>=3.0.0; extra == "hardware"
Requires-Dist: cantools>=36.0.0; extra == "hardware"
Provides-Extra: reports
Requires-Dist: reportlab>=3.6.0; extra == "reports"
Requires-Dist: jinja2>=3.1.0; extra == "reports"
Dynamic: author
Dynamic: home-page
Dynamic: requires-python

# Battery Testing Software

A comprehensive, modular battery testing application with clean Tkinter UI, designed for laboratories, EV pack manufacturers, and cell validation lines.

## Features

### Core Functionality
- **Multiple Test Profiles**: CC-CV, CP, EIS, Pulse, Drive Cycle Replay, Calendar Aging
- **Unlimited Channel Support**: Each channel runs independent test scripts
- **Real-time Monitoring**: Live plotting of voltage, current, temperature, capacity, SOC, SOH
- **Safety Interlocks**: Over-voltage, over-current, over-temperature protection with emergency stop
- **Data Logging**: SQLite database with optional PostgreSQL/MySQL support
- **Export Capabilities**: CSV, Excel, JSON formats with auto-export options
- **Report Generation**: PDF/HTML reports with configurable templates
- **Role-based Access**: Admin/Operator/Viewer roles with audit trail

### Architecture
- **Clean Onion/Hexagonal Architecture**: Clear separation of domain, application, and infrastructure layers
- **Async IO**: Non-blocking communications for responsive UI
- **Plugin Manager**: Extensible driver architecture for hardware integration
- **Hardware Abstraction**: Support for Arbin, Bitrode, MACCOR, Neware, Chroma, and custom hardware
- **Mock Hardware**: Built-in simulation for offline development and testing

### User Interface
- **Dark Theme**: Professional dark mode interface with high contrast options
- **Dashboard**: Channel status tiles, global alarms, system overview
- **Drag-and-Drop**: Visual test profile builder (planned feature)
- **Real-time Plots**: Multi-channel data visualization with zoom and pan
- **Safety Monitoring**: Visual safety status indicators and alarm management

## Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Quick Install
```bash
# Clone or download the repository
# Navigate to the project directory

# Install dependencies
pip install -r requirements.txt

# Run the application
python battery_testing_software.py
```

### Development Install
```bash
# Install with development dependencies
pip install -r requirements.txt
pip install pytest pytest-cov pytest-mock

# Run tests
python -m pytest test_battery_testing.py -v --cov=battery_testing_software
```

## Quick Start Guide

### 1. First Launch
1. Run `python battery_testing_software.py`
2. The application will start with mock hardware (8 channels)
3. Click "▶ Start DAQ" to begin data acquisition
4. Select a channel from the left panel

### 2. Configure a Test
1. Select test profile (CC-CV, CP, EIS, etc.)
2. Set target parameters:
   - **Voltage**: Target voltage in volts (e.g., 3.7V)
   - **Current**: Target current in amperes (e.g., 1.0A)
   - **Duration**: Test duration in minutes (e.g., 60 min)
3. Click "▶ Start Test"

### 3. Monitor Progress
- **Real-time Plots**: View live voltage, current, temperature, and SOC data
- **Channel Status**: Monitor test status in the channel list
- **Safety Alerts**: Automatic notifications for safety limit violations
- **Data Table**: Recent measurements displayed in tabular format

### 4. Safety Features
- **Emergency Stop**: Red button stops all channels immediately
- **Safety Limits**: Configurable voltage, current, and temperature limits
- **Automatic Protection**: Tests automatically stop on safety violations
- **Audit Trail**: All safety events logged with timestamps

### 5. Data Export
1. Click "Export Data" in the toolbar
2. Select channel and file format
3. Choose save location
4. Data exported with timestamps and all measurements

## Configuration

### Safety Limits
Access via Settings menu:
- **Max Voltage**: 4.2V (default)
- **Min Voltage**: 2.5V (default)
- **Max Current**: 10.0A (default)
- **Max Temperature**: 60°C (default)
- **Min Temperature**: -20°C (default)

### Hardware Configuration
Edit `battery_test_config.ini`:
```ini
[HARDWARE]
driver_type = mock
connection_timeout = 5.0
data_acquisition_rate = 10.0

[SAFETY]
max_voltage = 4.2
min_voltage = 2.5
max_current = 10.0
max_temperature = 60.0

[DATABASE]
type = sqlite
path = battery_test_data.db
backup_interval = 3600
```

## Test Profiles

### Constant Current - Constant Voltage (CC-CV)
Standard lithium-ion charging profile:
1. Constant current phase until target voltage
2. Constant voltage phase until current drops

### Constant Power (CP)
Maintains constant power output:
- Automatically adjusts voltage and current
- Useful for power-based testing scenarios

### Electrochemical Impedance Spectroscopy (EIS)
Frequency sweep testing:
- Measures battery impedance characteristics
- Useful for battery health assessment

### Pulse Testing
Intermittent current pulses:
- Configurable pulse width and amplitude
- Measures dynamic response characteristics

### Drive Cycle Replay
Replays real-world usage patterns:
- Load pre-recorded current profiles
- Simulates actual application conditions

### Calendar Aging
Long-term storage testing:
- Minimal current with periodic measurements
- Monitors capacity fade over time

## Hardware Integration

### Supported Hardware
- **Mock Driver**: Built-in simulation (default)
- **Arbin**: Professional battery testers
- **Bitrode**: High-power test systems
- **MACCOR**: Precision battery analyzers
- **Neware**: Cost-effective test equipment
- **Chroma**: Electronic load systems
- **Custom**: Arduino/Raspberry Pi based systems

### Adding New Hardware
1. Inherit from `HardwareDriver` base class
2. Implement required methods:
   - `connect()` / `disconnect()`
   - `read_measurement(channel_id)`
   - `set_output(channel_id, voltage, current)`
   - `get_channel_count()`
3. Register driver in configuration

## Database Schema

### Measurements Table
- `timestamp`: ISO format timestamp
- `channel_id`: Channel identifier
- `voltage`: Voltage measurement (V)
- `current`: Current measurement (A)
- `temperature`: Temperature (°C)
- `capacity`: Calculated capacity (Ah)
- `soc`: State of charge (%)
- `soh`: State of health (%)

### Safety Events Table
- `timestamp`: Event timestamp
- `channel_id`: Affected channel
- `event_type`: Type of safety event
- `severity`: Normal/Warning/Critical/Emergency
- `value`: Measured value
- `limit_value`: Safety limit exceeded

## Testing

### Run Unit Tests
```bash
python -m pytest test_battery_testing.py -v
```

### Run with Coverage
```bash
python -m pytest test_battery_testing.py --cov=battery_testing_software --cov-report=html
```

### Test Categories
- **Domain Layer**: Core business logic
- **Infrastructure**: Hardware drivers and database
- **Application**: Service coordination
- **Integration**: End-to-end workflows

## Troubleshooting

### Common Issues

**Application won't start**
- Check Python version (3.8+ required)
- Install missing dependencies: `pip install -r requirements.txt`
- Check for conflicting packages

**No data appearing**
- Ensure "Start DAQ" is clicked
- Check hardware connection status
- Verify channel selection

**Safety alerts not working**
- Check safety limit configuration
- Verify measurement values are realistic
- Review safety event logs in database

**Export fails**
- Check file permissions in target directory
- Ensure sufficient disk space
- Verify database contains data for selected channel

### Log Files
- Application logs: `battery_testing.log`
- Database file: `battery_test_data.db`
- Configuration: `battery_test_config.ini`

## Contributing

### Development Setup
1. Fork the repository
2. Create feature branch
3. Install development dependencies
4. Run tests before committing
5. Follow Clean Architecture principles

### Code Style
- Follow PEP 8 guidelines
- Use type hints where appropriate
- Document all public methods
- Maintain test coverage ≥80%

## License

This software is provided as-is for educational and research purposes.
For commercial use, please contact the development team.

## Support

For technical support or feature requests:
- Review documentation and troubleshooting guide
- Check existing issues in the repository
- Create detailed bug reports with logs and steps to reproduce

---

**Battery Testing Software v1.0**  
*Professional battery testing made simple*
