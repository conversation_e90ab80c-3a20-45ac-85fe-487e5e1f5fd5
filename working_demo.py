#!/usr/bin/env python3
"""
Working Demo - Premium Battery Testing Software
सगळे features काम करतात आणि सुंदर colors आहेत!
"""

import tkinter as tk
from tkinter import ttk, messagebox
import time
import threading
import random
import math
from datetime import datetime

class WorkingBatteryTester:
    """Simple working battery tester with beautiful UI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔋 Working Battery Testing Software - Beautiful Edition")
        self.root.geometry("1200x800")
        self.root.configure(bg='#0f1419')
        
        # Beautiful colors
        self.colors = {
            'bg_dark': '#0f1419',
            'bg_medium': '#1e2328', 
            'bg_light': '#2a2d32',
            'bg_card': '#252a30',
            'accent_blue': '#00d4ff',
            'accent_green': '#00ff88',
            'accent_red': '#ff4757',
            'accent_orange': '#ffa502',
            'accent_purple': '#a55eea',
            'text_white': '#ffffff',
            'text_gray': '#8b949e'
        }
        
        # Data storage
        self.channels = 8
        self.running = False
        self.selected_channel = 0
        self.data = {i: {'voltage': [], 'current': [], 'temperature': [], 'time': []} 
                    for i in range(self.channels)}
        
        self.setup_gui()
        self.start_simulation()
        
    def setup_gui(self):
        """Setup beautiful GUI"""
        
        # Header
        header = tk.Frame(self.root, bg=self.colors['accent_blue'], height=80)
        header.pack(fill=tk.X)
        header.pack_propagate(False)
        
        title = tk.Label(
            header,
            text="🔋 Premium Battery Testing Software",
            bg=self.colors['accent_blue'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 18, 'bold')
        )
        title.pack(pady=20)
        
        # Main content
        main_frame = tk.Frame(self.root, bg=self.colors['bg_dark'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Left panel - Controls
        left_panel = tk.Frame(main_frame, bg=self.colors['bg_medium'], width=300)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_panel.pack_propagate(False)
        
        self.setup_controls(left_panel)
        
        # Right panel - Data display
        right_panel = tk.Frame(main_frame, bg=self.colors['bg_medium'])
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.setup_data_display(right_panel)
        
    def setup_controls(self, parent):
        """Setup control panel"""
        
        # Title
        title = tk.Label(
            parent,
            text="🎛️ Control Panel",
            bg=self.colors['bg_medium'],
            fg=self.colors['accent_blue'],
            font=('Segoe UI', 14, 'bold')
        )
        title.pack(pady=20)
        
        # Channel selection
        tk.Label(
            parent,
            text="Select Channel:",
            bg=self.colors['bg_medium'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 11)
        ).pack(pady=(10, 5))
        
        self.channel_var = tk.StringVar(value="Channel 0")
        channel_combo = ttk.Combobox(
            parent,
            textvariable=self.channel_var,
            values=[f"Channel {i}" for i in range(self.channels)],
            state="readonly",
            width=20
        )
        channel_combo.pack(pady=5)
        channel_combo.bind('<<ComboboxSelected>>', self.on_channel_select)
        
        # Test parameters
        params_frame = tk.Frame(parent, bg=self.colors['bg_medium'])
        params_frame.pack(pady=20, padx=20, fill=tk.X)
        
        # Voltage
        tk.Label(
            params_frame,
            text="Target Voltage (V):",
            bg=self.colors['bg_medium'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 10)
        ).pack(anchor='w')
        
        self.voltage_entry = tk.Entry(
            params_frame,
            bg=self.colors['bg_card'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 10),
            width=15
        )
        self.voltage_entry.insert(0, "3.7")
        self.voltage_entry.pack(pady=(5, 10))
        
        # Current
        tk.Label(
            params_frame,
            text="Target Current (A):",
            bg=self.colors['bg_medium'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 10)
        ).pack(anchor='w')
        
        self.current_entry = tk.Entry(
            params_frame,
            bg=self.colors['bg_card'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 10),
            width=15
        )
        self.current_entry.insert(0, "1.0")
        self.current_entry.pack(pady=(5, 10))
        
        # Control buttons
        button_frame = tk.Frame(parent, bg=self.colors['bg_medium'])
        button_frame.pack(pady=20)
        
        self.start_btn = tk.Button(
            button_frame,
            text="▶ Start Test",
            bg=self.colors['accent_green'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 11, 'bold'),
            width=12,
            command=self.start_test
        )
        self.start_btn.pack(pady=5)
        
        self.stop_btn = tk.Button(
            button_frame,
            text="⏹ Stop Test",
            bg=self.colors['accent_red'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 11, 'bold'),
            width=12,
            command=self.stop_test
        )
        self.stop_btn.pack(pady=5)
        
        # Status display
        status_frame = tk.Frame(parent, bg=self.colors['bg_card'])
        status_frame.pack(pady=20, padx=20, fill=tk.X)
        
        tk.Label(
            status_frame,
            text="📊 Current Status",
            bg=self.colors['bg_card'],
            fg=self.colors['accent_purple'],
            font=('Segoe UI', 12, 'bold')
        ).pack(pady=10)
        
        self.voltage_label = tk.Label(
            status_frame,
            text="⚡ Voltage: -- V",
            bg=self.colors['bg_card'],
            fg=self.colors['accent_blue'],
            font=('Segoe UI', 10)
        )
        self.voltage_label.pack(pady=2)
        
        self.current_label = tk.Label(
            status_frame,
            text="🔋 Current: -- A",
            bg=self.colors['bg_card'],
            fg=self.colors['accent_green'],
            font=('Segoe UI', 10)
        )
        self.current_label.pack(pady=2)
        
        self.temp_label = tk.Label(
            status_frame,
            text="🌡️ Temperature: -- °C",
            bg=self.colors['bg_card'],
            fg=self.colors['accent_orange'],
            font=('Segoe UI', 10)
        )
        self.temp_label.pack(pady=2)
        
    def setup_data_display(self, parent):
        """Setup data display area"""
        
        # Title
        title = tk.Label(
            parent,
            text="📈 Real-time Data",
            bg=self.colors['bg_medium'],
            fg=self.colors['accent_blue'],
            font=('Segoe UI', 14, 'bold')
        )
        title.pack(pady=20)
        
        # Data table
        table_frame = tk.Frame(parent, bg=self.colors['bg_medium'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Create treeview
        columns = ('Time', 'Voltage', 'Current', 'Temperature')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor='center')
            
        # Scrollbar
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Log area
        log_frame = tk.Frame(parent, bg=self.colors['bg_card'])
        log_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        tk.Label(
            log_frame,
            text="📝 Test Log",
            bg=self.colors['bg_card'],
            fg=self.colors['accent_purple'],
            font=('Segoe UI', 12, 'bold')
        ).pack(pady=(10, 5))
        
        self.log_text = tk.Text(
            log_frame,
            bg=self.colors['bg_dark'],
            fg=self.colors['text_white'],
            font=('Consolas', 9),
            height=6,
            wrap=tk.WORD
        )
        self.log_text.pack(fill=tk.X, padx=10, pady=(0, 10))
        
    def start_simulation(self):
        """Start data simulation"""
        self.running = True
        self.sim_thread = threading.Thread(target=self.simulation_loop, daemon=True)
        self.sim_thread.start()
        self.update_gui()
        
    def simulation_loop(self):
        """Simulate battery data"""
        while self.running:
            for channel in range(self.channels):
                # Generate realistic data
                base_voltage = 3.7
                base_current = 1.0
                base_temp = 25.0
                
                # Add some variation
                voltage = base_voltage + random.uniform(-0.1, 0.1) + 0.05 * math.sin(time.time() * 0.1)
                current = base_current + random.uniform(-0.05, 0.05)
                temperature = base_temp + random.uniform(-2, 2)
                
                # Store data
                current_time = datetime.now().strftime('%H:%M:%S')
                self.data[channel]['time'].append(current_time)
                self.data[channel]['voltage'].append(voltage)
                self.data[channel]['current'].append(current)
                self.data[channel]['temperature'].append(temperature)
                
                # Keep only last 50 points
                for key in self.data[channel]:
                    if len(self.data[channel][key]) > 50:
                        self.data[channel][key] = self.data[channel][key][-50:]
                        
            time.sleep(1.0)  # Update every second
            
    def update_gui(self):
        """Update GUI with current data"""
        try:
            # Update status labels
            if self.data[self.selected_channel]['voltage']:
                latest_voltage = self.data[self.selected_channel]['voltage'][-1]
                latest_current = self.data[self.selected_channel]['current'][-1]
                latest_temp = self.data[self.selected_channel]['temperature'][-1]
                
                self.voltage_label.config(text=f"⚡ Voltage: {latest_voltage:.3f} V")
                self.current_label.config(text=f"🔋 Current: {latest_current:.3f} A")
                self.temp_label.config(text=f"🌡️ Temperature: {latest_temp:.1f} °C")
                
            # Update data table
            self.update_table()
            
        except Exception as e:
            print(f"GUI update error: {e}")
            
        # Schedule next update
        self.root.after(1000, self.update_gui)
        
    def update_table(self):
        """Update data table"""
        # Clear existing data
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # Add recent data (last 20 points)
        channel_data = self.data[self.selected_channel]
        if channel_data['time']:
            recent_count = min(20, len(channel_data['time']))
            for i in range(recent_count):
                idx = -(recent_count - i)
                self.tree.insert('', 'end', values=(
                    channel_data['time'][idx],
                    f"{channel_data['voltage'][idx]:.3f}",
                    f"{channel_data['current'][idx]:.3f}",
                    f"{channel_data['temperature'][idx]:.1f}"
                ))
                
    def on_channel_select(self, event):
        """Handle channel selection"""
        channel_text = self.channel_var.get()
        self.selected_channel = int(channel_text.split()[1])
        self.log_text.insert(tk.END, f"📍 Selected {channel_text}\n")
        self.log_text.see(tk.END)
        
    def start_test(self):
        """Start test"""
        try:
            voltage = float(self.voltage_entry.get())
            current = float(self.current_entry.get())
            
            self.log_text.insert(tk.END, f"✅ Started test on Channel {self.selected_channel}\n")
            self.log_text.insert(tk.END, f"🎯 Target: {voltage}V, {current}A\n")
            self.log_text.insert(tk.END, f"🕐 Time: {datetime.now().strftime('%H:%M:%S')}\n\n")
            self.log_text.see(tk.END)
            
            messagebox.showinfo("Test Started", f"Test started on Channel {self.selected_channel}")
            
        except ValueError:
            messagebox.showerror("Error", "Please enter valid voltage and current values")
            
    def stop_test(self):
        """Stop test"""
        self.log_text.insert(tk.END, f"⏹️ Stopped test on Channel {self.selected_channel}\n")
        self.log_text.insert(tk.END, f"🕐 Time: {datetime.now().strftime('%H:%M:%S')}\n\n")
        self.log_text.see(tk.END)
        
        messagebox.showinfo("Test Stopped", f"Test stopped on Channel {self.selected_channel}")
        
    def run(self):
        """Run the application"""
        self.root.mainloop()

def main():
    """Main function"""
    print("🔋 Starting Working Battery Testing Software...")
    print("✨ Beautiful colors and working features!")
    
    app = WorkingBatteryTester()
    app.run()

if __name__ == "__main__":
    main()
