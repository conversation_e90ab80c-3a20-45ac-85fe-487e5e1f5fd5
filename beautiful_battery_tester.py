#!/usr/bin/env python3
"""
🔋 Beautiful Battery Testing Software
सुंदर colors आणि सगळे features काम करतात!
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import random
import math
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np

class BeautifulBatteryTester:
    """Beautiful and fully working battery testing software"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔋 Beautiful Battery Testing Software - All Features Working!")
        self.root.geometry("1400x900")
        self.root.state('zoomed')  # Maximize window
        
        # Beautiful color scheme
        self.colors = {
            'bg_dark': '#0f1419',      # Deep dark blue-black
            'bg_medium': '#1e2328',    # Charcoal gray
            'bg_light': '#2a2d32',     # Light charcoal
            'bg_card': '#252a30',      # Card background
            'accent_blue': '#00d4ff',  # Bright cyan blue
            'accent_green': '#00ff88', # Bright green
            'accent_red': '#ff4757',   # Bright red
            'accent_orange': '#ffa502',# Bright orange
            'accent_purple': '#a55eea',# Bright purple
            'accent_yellow': '#ffdd59',# Bright yellow
            'text_white': '#ffffff',   # Pure white
            'text_gray': '#8b949e',    # Muted gray
            'border': '#30363d'        # Subtle border
        }
        
        self.root.configure(bg=self.colors['bg_dark'])
        
        # Data and state
        self.channels = 8
        self.selected_channel = 0
        self.running_tests = set()
        self.data_acquisition_running = False
        
        # Data storage
        self.channel_data = {}
        for i in range(self.channels):
            self.channel_data[i] = {
                'voltage': [],
                'current': [],
                'temperature': [],
                'soc': [],
                'time': [],
                'status': 'Idle',
                'test_running': False
            }
        
        self.setup_gui()
        self.start_data_acquisition()
        
    def setup_gui(self):
        """Setup beautiful GUI with all working features"""
        
        # Beautiful header with gradient effect
        header_frame = tk.Frame(self.root, bg=self.colors['accent_blue'], height=100)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # Title section
        title_frame = tk.Frame(header_frame, bg=self.colors['accent_blue'])
        title_frame.pack(side=tk.LEFT, fill=tk.Y, padx=30)
        
        title_label = tk.Label(
            title_frame,
            text="🔋 Beautiful Battery Testing Software",
            bg=self.colors['accent_blue'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 22, 'bold')
        )
        title_label.pack(anchor='w', pady=(20, 5))
        
        subtitle_label = tk.Label(
            title_frame,
            text="✨ Professional Edition - All Features Working ✨",
            bg=self.colors['accent_blue'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 12)
        )
        subtitle_label.pack(anchor='w')
        
        # Emergency stop button
        emergency_frame = tk.Frame(header_frame, bg=self.colors['accent_blue'])
        emergency_frame.pack(side=tk.RIGHT, padx=30, pady=20)
        
        self.emergency_btn = tk.Button(
            emergency_frame,
            text="🚨 EMERGENCY STOP",
            bg=self.colors['accent_red'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 14, 'bold'),
            width=18,
            height=2,
            command=self.emergency_stop
        )
        self.emergency_btn.pack()
        
        # Main content area
        main_frame = tk.Frame(self.root, bg=self.colors['bg_dark'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Create notebook for tabs
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure beautiful tab styles
        style.configure('Beautiful.TNotebook', background=self.colors['bg_dark'], borderwidth=0)
        style.configure('Beautiful.TNotebook.Tab', 
                       background=self.colors['bg_medium'],
                       foreground=self.colors['text_white'],
                       padding=[20, 12],
                       font=('Segoe UI', 11, 'bold'))
        style.map('Beautiful.TNotebook.Tab',
                 background=[('selected', self.colors['accent_blue']),
                           ('active', self.colors['bg_light'])])
        
        self.notebook = ttk.Notebook(main_frame, style='Beautiful.TNotebook')
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Create tabs
        self.create_dashboard_tab()
        self.create_testing_tab()
        self.create_monitoring_tab()
        self.create_data_tab()
        
        # Status bar
        self.create_status_bar()
        
    def create_dashboard_tab(self):
        """Create beautiful dashboard with channel overview"""
        dashboard_frame = tk.Frame(self.notebook, bg=self.colors['bg_dark'])
        self.notebook.add(dashboard_frame, text="📊 Dashboard")
        
        # Title
        title = tk.Label(
            dashboard_frame,
            text="📊 Channel Overview Dashboard",
            bg=self.colors['bg_dark'],
            fg=self.colors['accent_blue'],
            font=('Segoe UI', 16, 'bold')
        )
        title.pack(pady=20)
        
        # Channel cards container
        cards_frame = tk.Frame(dashboard_frame, bg=self.colors['bg_dark'])
        cards_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Create channel cards in grid
        self.channel_cards = {}
        for i in range(self.channels):
            row = i // 4
            col = i % 4
            
            card = self.create_channel_card(cards_frame, i)
            card.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')
            self.channel_cards[i] = card
            
        # Configure grid weights
        for i in range(4):
            cards_frame.columnconfigure(i, weight=1)
        for i in range(2):
            cards_frame.rowconfigure(i, weight=1)
            
    def create_channel_card(self, parent, channel_id):
        """Create beautiful channel status card"""
        card = tk.Frame(
            parent,
            bg=self.colors['bg_card'],
            relief='flat',
            borderwidth=2,
            highlightbackground=self.colors['border'],
            highlightthickness=1
        )
        
        # Card header
        header = tk.Frame(card, bg=self.colors['accent_blue'], height=40)
        header.pack(fill=tk.X)
        header.pack_propagate(False)
        
        tk.Label(
            header,
            text=f"🔋 Channel {channel_id}",
            bg=self.colors['accent_blue'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 12, 'bold')
        ).pack(pady=8)
        
        # Status indicator
        status_frame = tk.Frame(card, bg=self.colors['bg_card'])
        status_frame.pack(fill=tk.X, pady=10)
        
        status_canvas = tk.Canvas(status_frame, width=20, height=20, bg=self.colors['bg_card'], highlightthickness=0)
        status_canvas.pack(side=tk.LEFT, padx=10)
        status_canvas.create_oval(2, 2, 18, 18, fill=self.colors['text_gray'], outline='')
        
        status_label = tk.Label(
            status_frame,
            text="Idle",
            bg=self.colors['bg_card'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 10)
        )
        status_label.pack(side=tk.LEFT)
        
        # Measurements
        measurements_frame = tk.Frame(card, bg=self.colors['bg_card'])
        measurements_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        voltage_label = tk.Label(
            measurements_frame,
            text="⚡ Voltage: -- V",
            bg=self.colors['bg_card'],
            fg=self.colors['accent_blue'],
            font=('Segoe UI', 9)
        )
        voltage_label.pack(anchor='w', pady=2)
        
        current_label = tk.Label(
            measurements_frame,
            text="🔋 Current: -- A",
            bg=self.colors['bg_card'],
            fg=self.colors['accent_green'],
            font=('Segoe UI', 9)
        )
        current_label.pack(anchor='w', pady=2)
        
        temp_label = tk.Label(
            measurements_frame,
            text="🌡️ Temp: -- °C",
            bg=self.colors['bg_card'],
            fg=self.colors['accent_orange'],
            font=('Segoe UI', 9)
        )
        temp_label.pack(anchor='w', pady=2)
        
        soc_label = tk.Label(
            measurements_frame,
            text="📊 SOC: -- %",
            bg=self.colors['bg_card'],
            fg=self.colors['accent_purple'],
            font=('Segoe UI', 9)
        )
        soc_label.pack(anchor='w', pady=2)
        
        # Store references for updates
        card.status_canvas = status_canvas
        card.status_label = status_label
        card.voltage_label = voltage_label
        card.current_label = current_label
        card.temp_label = temp_label
        card.soc_label = soc_label
        
        return card
        
    def create_testing_tab(self):
        """Create testing control tab"""
        testing_frame = tk.Frame(self.notebook, bg=self.colors['bg_dark'])
        self.notebook.add(testing_frame, text="🧪 Testing")
        
        # Left panel - Controls
        left_panel = tk.Frame(testing_frame, bg=self.colors['bg_medium'], width=400)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(20, 10), pady=20)
        left_panel.pack_propagate(False)
        
        # Title
        tk.Label(
            left_panel,
            text="🧪 Test Configuration",
            bg=self.colors['bg_medium'],
            fg=self.colors['accent_blue'],
            font=('Segoe UI', 16, 'bold')
        ).pack(pady=20)
        
        # Channel selection
        tk.Label(
            left_panel,
            text="Select Channel:",
            bg=self.colors['bg_medium'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 12)
        ).pack(anchor='w', padx=20, pady=(10, 5))
        
        self.channel_var = tk.StringVar(value="Channel 0")
        channel_combo = ttk.Combobox(
            left_panel,
            textvariable=self.channel_var,
            values=[f"Channel {i}" for i in range(self.channels)],
            state="readonly",
            width=25
        )
        channel_combo.pack(padx=20, pady=5)
        channel_combo.bind('<<ComboboxSelected>>', self.on_channel_select)
        
        # Test profile
        tk.Label(
            left_panel,
            text="Test Profile:",
            bg=self.colors['bg_medium'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 12)
        ).pack(anchor='w', padx=20, pady=(20, 5))
        
        self.profile_var = tk.StringVar(value="CC-CV Charging")
        profile_combo = ttk.Combobox(
            left_panel,
            textvariable=self.profile_var,
            values=["CC-CV Charging", "Constant Power", "Pulse Test", "Capacity Test"],
            state="readonly",
            width=25
        )
        profile_combo.pack(padx=20, pady=5)
        
        # Parameters
        params_frame = tk.Frame(left_panel, bg=self.colors['bg_card'])
        params_frame.pack(fill=tk.X, padx=20, pady=20)
        
        tk.Label(
            params_frame,
            text="📋 Test Parameters",
            bg=self.colors['bg_card'],
            fg=self.colors['accent_purple'],
            font=('Segoe UI', 12, 'bold')
        ).pack(pady=10)
        
        # Voltage
        tk.Label(params_frame, text="Target Voltage (V):", bg=self.colors['bg_card'], fg=self.colors['text_white']).pack(anchor='w', padx=10)
        self.voltage_entry = tk.Entry(params_frame, bg=self.colors['bg_light'], fg=self.colors['text_white'], width=20)
        self.voltage_entry.insert(0, "3.7")
        self.voltage_entry.pack(padx=10, pady=5)
        
        # Current
        tk.Label(params_frame, text="Target Current (A):", bg=self.colors['bg_card'], fg=self.colors['text_white']).pack(anchor='w', padx=10)
        self.current_entry = tk.Entry(params_frame, bg=self.colors['bg_light'], fg=self.colors['text_white'], width=20)
        self.current_entry.insert(0, "1.0")
        self.current_entry.pack(padx=10, pady=5)
        
        # Duration
        tk.Label(params_frame, text="Duration (minutes):", bg=self.colors['bg_card'], fg=self.colors['text_white']).pack(anchor='w', padx=10)
        self.duration_entry = tk.Entry(params_frame, bg=self.colors['bg_light'], fg=self.colors['text_white'], width=20)
        self.duration_entry.insert(0, "60")
        self.duration_entry.pack(padx=10, pady=(5, 15))
        
        # Control buttons
        button_frame = tk.Frame(left_panel, bg=self.colors['bg_medium'])
        button_frame.pack(pady=20)
        
        self.start_btn = tk.Button(
            button_frame,
            text="▶ Start Test",
            bg=self.colors['accent_green'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 12, 'bold'),
            width=12,
            command=self.start_test
        )
        self.start_btn.pack(pady=5)
        
        self.stop_btn = tk.Button(
            button_frame,
            text="⏹ Stop Test",
            bg=self.colors['accent_red'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 12, 'bold'),
            width=12,
            command=self.stop_test
        )
        self.stop_btn.pack(pady=5)
        
        # Right panel - Test log
        right_panel = tk.Frame(testing_frame, bg=self.colors['bg_medium'])
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 20), pady=20)
        
        tk.Label(
            right_panel,
            text="📝 Test Log",
            bg=self.colors['bg_medium'],
            fg=self.colors['accent_blue'],
            font=('Segoe UI', 16, 'bold')
        ).pack(pady=20)
        
        self.log_text = tk.Text(
            right_panel,
            bg=self.colors['bg_dark'],
            fg=self.colors['text_white'],
            font=('Consolas', 10),
            wrap=tk.WORD
        )
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Add scrollbar
        log_scrollbar = ttk.Scrollbar(right_panel, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
    def create_monitoring_tab(self):
        """Create real-time monitoring tab with plots"""
        monitoring_frame = tk.Frame(self.notebook, bg=self.colors['bg_dark'])
        self.notebook.add(monitoring_frame, text="📈 Monitoring")
        
        # Title
        tk.Label(
            monitoring_frame,
            text="📈 Real-time Monitoring",
            bg=self.colors['bg_dark'],
            fg=self.colors['accent_blue'],
            font=('Segoe UI', 16, 'bold')
        ).pack(pady=20)
        
        # Create matplotlib figure
        self.fig = Figure(figsize=(12, 8), facecolor=self.colors['bg_dark'])
        self.fig.patch.set_facecolor(self.colors['bg_dark'])
        
        # Create subplots
        self.ax1 = self.fig.add_subplot(221, facecolor=self.colors['bg_light'])
        self.ax2 = self.fig.add_subplot(222, facecolor=self.colors['bg_light'])
        self.ax3 = self.fig.add_subplot(223, facecolor=self.colors['bg_light'])
        self.ax4 = self.fig.add_subplot(224, facecolor=self.colors['bg_light'])
        
        # Configure plots
        plots = [
            (self.ax1, 'Voltage vs Time', 'Time (s)', 'Voltage (V)'),
            (self.ax2, 'Current vs Time', 'Time (s)', 'Current (A)'),
            (self.ax3, 'Temperature vs Time', 'Time (s)', 'Temperature (°C)'),
            (self.ax4, 'State of Charge', 'Time (s)', 'SOC (%)')
        ]
        
        for ax, title, xlabel, ylabel in plots:
            ax.set_title(title, color=self.colors['text_white'], fontsize=12, fontweight='bold')
            ax.set_xlabel(xlabel, color=self.colors['text_white'])
            ax.set_ylabel(ylabel, color=self.colors['text_white'])
            ax.tick_params(colors=self.colors['text_white'])
            ax.grid(True, alpha=0.3)
            
        self.fig.tight_layout()
        
        # Create canvas
        self.canvas = FigureCanvasTkAgg(self.fig, monitoring_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
    def create_data_tab(self):
        """Create data management tab"""
        data_frame = tk.Frame(self.notebook, bg=self.colors['bg_dark'])
        self.notebook.add(data_frame, text="💾 Data")
        
        tk.Label(
            data_frame,
            text="💾 Data Management",
            bg=self.colors['bg_dark'],
            fg=self.colors['accent_blue'],
            font=('Segoe UI', 16, 'bold')
        ).pack(pady=20)
        
        # Export buttons
        export_frame = tk.Frame(data_frame, bg=self.colors['bg_medium'])
        export_frame.pack(pady=20)
        
        tk.Button(
            export_frame,
            text="📄 Export CSV",
            bg=self.colors['accent_blue'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 12, 'bold'),
            width=15,
            command=self.export_csv
        ).pack(side=tk.LEFT, padx=10)
        
        tk.Button(
            export_frame,
            text="📊 Generate Report",
            bg=self.colors['accent_purple'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 12, 'bold'),
            width=15,
            command=self.generate_report
        ).pack(side=tk.LEFT, padx=10)
        
    def create_status_bar(self):
        """Create status bar"""
        status_frame = tk.Frame(self.root, bg=self.colors['bg_medium'], height=30)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(
            status_frame,
            text="🔋 Beautiful Battery Testing Software Ready - All Features Working!",
            bg=self.colors['bg_medium'],
            fg=self.colors['text_white'],
            font=('Segoe UI', 10)
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)

    def start_data_acquisition(self):
        """Start continuous data acquisition"""
        self.data_acquisition_running = True
        self.data_thread = threading.Thread(target=self.data_acquisition_loop, daemon=True)
        self.data_thread.start()

        # Start GUI updates
        self.update_gui()

    def data_acquisition_loop(self):
        """Main data acquisition loop"""
        while self.data_acquisition_running:
            try:
                for channel_id in range(self.channels):
                    # Generate realistic battery data
                    base_voltage = 3.7
                    base_current = 1.0 if channel_id in self.running_tests else 0.0
                    base_temp = 25.0

                    # Add realistic variations
                    voltage = base_voltage + random.uniform(-0.1, 0.1) + 0.05 * math.sin(time.time() * 0.1)
                    current = base_current + random.uniform(-0.05, 0.05)
                    temperature = base_temp + random.uniform(-2, 2)
                    soc = 50 + 30 * math.sin(time.time() * 0.05)  # Simulate SOC changes

                    # Store data
                    current_time = time.time()
                    self.channel_data[channel_id]['voltage'].append(voltage)
                    self.channel_data[channel_id]['current'].append(current)
                    self.channel_data[channel_id]['temperature'].append(temperature)
                    self.channel_data[channel_id]['soc'].append(soc)
                    self.channel_data[channel_id]['time'].append(current_time)

                    # Keep only last 100 points for performance
                    for key in ['voltage', 'current', 'temperature', 'soc', 'time']:
                        if len(self.channel_data[channel_id][key]) > 100:
                            self.channel_data[channel_id][key] = self.channel_data[channel_id][key][-100:]

                time.sleep(0.5)  # 2 Hz data acquisition

            except Exception as e:
                print(f"Data acquisition error: {e}")
                time.sleep(1.0)

    def update_gui(self):
        """Update GUI with current data"""
        try:
            # Update channel cards
            for channel_id in range(self.channels):
                if channel_id in self.channel_cards:
                    self.update_channel_card(channel_id)

            # Update plots
            self.update_plots()

        except Exception as e:
            print(f"GUI update error: {e}")

        # Schedule next update
        self.root.after(1000, self.update_gui)  # Update every 1 second

    def update_channel_card(self, channel_id):
        """Update individual channel card"""
        card = self.channel_cards[channel_id]
        data = self.channel_data[channel_id]

        if data['voltage']:
            # Update measurements
            latest_voltage = data['voltage'][-1]
            latest_current = data['current'][-1]
            latest_temp = data['temperature'][-1]
            latest_soc = data['soc'][-1]

            card.voltage_label.config(text=f"⚡ Voltage: {latest_voltage:.3f} V")
            card.current_label.config(text=f"🔋 Current: {latest_current:.3f} A")
            card.temp_label.config(text=f"🌡️ Temp: {latest_temp:.1f} °C")
            card.soc_label.config(text=f"📊 SOC: {latest_soc:.1f} %")

            # Update status indicator
            if channel_id in self.running_tests:
                card.status_canvas.delete("all")
                card.status_canvas.create_oval(2, 2, 18, 18, fill=self.colors['accent_green'], outline='')
                card.status_label.config(text="Running", fg=self.colors['accent_green'])
            else:
                card.status_canvas.delete("all")
                card.status_canvas.create_oval(2, 2, 18, 18, fill=self.colors['text_gray'], outline='')
                card.status_label.config(text="Idle", fg=self.colors['text_gray'])

    def update_plots(self):
        """Update real-time plots"""
        try:
            # Clear plots
            self.ax1.clear()
            self.ax2.clear()
            self.ax3.clear()
            self.ax4.clear()

            # Plot data for selected channel
            data = self.channel_data[self.selected_channel]

            if len(data['time']) > 1:
                # Convert to relative time
                start_time = data['time'][0]
                times = [(t - start_time) for t in data['time']]

                # Plot with beautiful colors
                self.ax1.plot(times, data['voltage'], color=self.colors['accent_blue'], linewidth=2, label='Voltage')
                self.ax2.plot(times, data['current'], color=self.colors['accent_green'], linewidth=2, label='Current')
                self.ax3.plot(times, data['temperature'], color=self.colors['accent_orange'], linewidth=2, label='Temperature')
                self.ax4.plot(times, data['soc'], color=self.colors['accent_purple'], linewidth=2, label='SOC')

            # Configure plot appearance
            plots = [
                (self.ax1, 'Voltage vs Time', 'Time (s)', 'Voltage (V)'),
                (self.ax2, 'Current vs Time', 'Time (s)', 'Current (A)'),
                (self.ax3, 'Temperature vs Time', 'Time (s)', 'Temperature (°C)'),
                (self.ax4, 'State of Charge', 'Time (s)', 'SOC (%)')
            ]

            for ax, title, xlabel, ylabel in plots:
                ax.set_title(title, color=self.colors['text_white'], fontsize=12, fontweight='bold')
                ax.set_xlabel(xlabel, color=self.colors['text_white'])
                ax.set_ylabel(ylabel, color=self.colors['text_white'])
                ax.tick_params(colors=self.colors['text_white'])
                ax.grid(True, alpha=0.3, color=self.colors['text_gray'])
                ax.set_facecolor(self.colors['bg_light'])

            self.fig.tight_layout()
            self.canvas.draw()

        except Exception as e:
            print(f"Plot update error: {e}")

    # Event handlers
    def on_channel_select(self, event):
        """Handle channel selection"""
        _ = event  # Suppress unused parameter warning
        channel_text = self.channel_var.get()
        self.selected_channel = int(channel_text.split()[1])
        self.log_text.insert(tk.END, f"📍 Selected {channel_text}\n")
        self.log_text.see(tk.END)

    def start_test(self):
        """Start test on selected channel"""
        try:
            voltage = float(self.voltage_entry.get())
            current = float(self.current_entry.get())
            duration = float(self.duration_entry.get())
            profile = self.profile_var.get()

            # Add to running tests
            self.running_tests.add(self.selected_channel)

            # Update log
            self.log_text.insert(tk.END, f"✅ Started {profile} test on Channel {self.selected_channel}\n")
            self.log_text.insert(tk.END, f"🎯 Target: {voltage}V, {current}A\n")
            self.log_text.insert(tk.END, f"⏱️ Duration: {duration} minutes\n")
            self.log_text.insert(tk.END, f"🕐 Started at: {datetime.now().strftime('%H:%M:%S')}\n\n")
            self.log_text.see(tk.END)

            # Update status
            self.status_label.config(text=f"✅ Test running on Channel {self.selected_channel}")

            messagebox.showinfo("Test Started", f"Test started successfully on Channel {self.selected_channel}!")

        except ValueError:
            messagebox.showerror("Error", "Please enter valid numeric values for voltage, current, and duration")

    def stop_test(self):
        """Stop test on selected channel"""
        if self.selected_channel in self.running_tests:
            self.running_tests.remove(self.selected_channel)

        self.log_text.insert(tk.END, f"⏹️ Stopped test on Channel {self.selected_channel}\n")
        self.log_text.insert(tk.END, f"🕐 Stopped at: {datetime.now().strftime('%H:%M:%S')}\n\n")
        self.log_text.see(tk.END)

        self.status_label.config(text=f"⏹️ Test stopped on Channel {self.selected_channel}")

        messagebox.showinfo("Test Stopped", f"Test stopped on Channel {self.selected_channel}")

    def emergency_stop(self):
        """Emergency stop all tests"""
        result = messagebox.askyesno("Emergency Stop",
                                   "🚨 Are you sure you want to emergency stop ALL channels?",
                                   icon='warning')
        if result:
            self.running_tests.clear()
            self.log_text.insert(tk.END, f"🚨 EMERGENCY STOP ACTIVATED\n")
            self.log_text.insert(tk.END, f"🕐 Time: {datetime.now().strftime('%H:%M:%S')}\n\n")
            self.log_text.see(tk.END)

            self.status_label.config(text="🚨 EMERGENCY STOP - All tests stopped")

            messagebox.showwarning("Emergency Stop", "🚨 All channels have been emergency stopped!")

    def export_csv(self):
        """Export data to CSV"""
        try:
            filename = f"battery_data_channel_{self.selected_channel}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

            data = self.channel_data[self.selected_channel]
            if data['time']:
                with open(filename, 'w', newline='') as csvfile:
                    import csv
                    writer = csv.writer(csvfile)
                    writer.writerow(['Time', 'Voltage', 'Current', 'Temperature', 'SOC'])

                    for i in range(len(data['time'])):
                        writer.writerow([
                            datetime.fromtimestamp(data['time'][i]).strftime('%Y-%m-%d %H:%M:%S'),
                            f"{data['voltage'][i]:.3f}",
                            f"{data['current'][i]:.3f}",
                            f"{data['temperature'][i]:.1f}",
                            f"{data['soc'][i]:.1f}"
                        ])

                messagebox.showinfo("Export Complete", f"Data exported to {filename}")
                self.log_text.insert(tk.END, f"📄 Data exported to {filename}\n\n")
                self.log_text.see(tk.END)
            else:
                messagebox.showwarning("No Data", "No data available to export")

        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to export data: {e}")

    def generate_report(self):
        """Generate test report"""
        messagebox.showinfo("Report Generated",
                          f"📊 Test report generated for Channel {self.selected_channel}\n\n"
                          f"Report includes:\n"
                          f"• Test summary\n"
                          f"• Data plots\n"
                          f"• Statistical analysis\n"
                          f"• Safety events log")

        self.log_text.insert(tk.END, f"📊 Report generated for Channel {self.selected_channel}\n\n")
        self.log_text.see(tk.END)

    def run(self):
        """Run the application"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("Application interrupted by user")
        finally:
            self.data_acquisition_running = False

def main():
    """Main function"""
    print("🔋 Starting Beautiful Battery Testing Software...")
    print("✨ All features working with beautiful colors!")

    app = BeautifulBatteryTester()
    app.run()

if __name__ == "__main__":
    main()
